# Changelog - Evidence Checklist OTE

## Verze 1.1.0 - <PERSON><PERSON><PERSON><PERSON><PERSON>š<PERSON><PERSON> a opravy (2025-01-27)

### ✅ Hlavní opravy a vylepšení

#### 🔧 Odstranění zbytečného výběru složky
- **Problém**: Aplikace vyžadovala výběr složky pro ukládání dat, což bylo mat<PERSON> a zbytečné
- **Řešení**: Odstraněn požadavek na výběr složky, data se ukládají lokálně v prohlížeči
- **Výhody**: Jednodušš<PERSON> workflow, rychlejší start aplikace

#### 📋 Aktualizace checklist obsahu
- **Problém**: Checklist neobsahoval správné kontrolní body pro OTE odstávky
- **Řešení**: Implementovány všechny 15 standardních kontrolních bodů podle specifikace
- **Nový obsah**:
  1. Kontrola přihlašovací stránky OTE market (ote-cr.cz)
  2. Interní test portálu
  3. Test podpisových certifikátů (DT, OTE-COM)
  4. Kontrola DT + AMQP (mailová fronta)
  5. OTE-COM (elektřina, plyn)
  6. Mobilní aplikace (POZE, VDT, VDP)
  7. SAP (CDP, KSM, KSP, OZP)
  8. Kontakt N4T4GAS (pí. Lerbleiterová 604 222 872)
  9. KSP - kontrola nominací
  10. PMB - kontrola statusů
  11. Nastavení vývěsky o ukončení odstávky
  12. Aktualizace informací o odstávce
  13. Testovací zpráva 621 (<EMAIL>)
  14. Ověření EZP portálu
  15. E-mail o ukončení odstávky

#### 🎯 Zlepšení uživatelského workflow
- **Problém**: Checklist se nezobrazoval po výběru odstávky
- **Řešení**: Opravena navigace a stav aplikace
- **Nový workflow**:
  1. Zadání jména (bez složky)
  2. Automatický přechod na odstávky
  3. Výběr odstávky ze seznamu
  4. Automatické zobrazení checklistu
  5. Dokončení a export

#### 💾 Zjednodušení ukládání dat
- **Změna**: Všechna data se ukládají lokálně v LocalStorage
- **Export**: Soubory se stahují přímo do Downloads složky
- **Formáty**: PDF, JSON, Excel, CSV

### 🗑️ Odstraněné funkce
- Výběr složky pro ukládání dat
- File System Access API fallback mechanismus
- Složitá kompatibilita s různými prohlížeči
- Nepotřebné komponenty (FolderSupportInfo, folderUtils)

### 🔄 Technické změny
- Odstraněn `folder` stav z aplikačního kontextu
- Zjednodušeny komponenty WelcomeCard a EnhancedWelcomeCard
- Aktualizovány export a storage služby
- Opravena navigace mezi sekcemi
- Vyčištěny nepotřebné importy a závislosti

### 📱 Uživatelské rozhraní
- Zjednodušený úvodní formulář (pouze jméno)
- Automatické zobrazení checklistu po výběru odstávky
- Lepší indikátory stavu v navigaci
- Konzistentnější UX napříč aplikací

### 🎯 Výsledek
Aplikace je nyní jednodušší na použití, má správný obsah checklistu a funguje podle očekávaného workflow:
**Jméno → Odstávky → Výběr odstávky → Checklist → Export**

---

## Verze 1.0.0 - Počáteční verze
- Základní funkcionalita aplikace
- React + Material-UI
- LocalStorage pro persistenci dat
- Export do PDF/JSON/Excel
- Správa odstávek a checklistů
