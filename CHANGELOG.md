# Changelog - Evidence Checklist OTE

## Verze 2.0.0 - Kompletní přepracování workflow (2025-01-27)

### 🚀 Nové funkce

#### 🔐 Jednoduchý login systém
- **Nová funkce**: Nahrazena úvodní stránka jednoduchým login formulářem
- **Výhoda**: Rychlejší přístup do aplikace, pouze zadání j<PERSON>na
- **UI**: Moderní login karta s OTE logem

#### ⚡ Automatické přesměrování na checklist
- **Nová funkce**: Po výběru odstávky se automaticky zobrazí checklist
- **Workflow**: Odstávky → Výběr → Okamžitě checklist
- **Výhoda**: Eliminuje zbytečné kroky

#### 🎯 Flexibilní stavy kontrolních bodů
- **Nová funkce**: 4 různé stavy místo pouze zaškrtnutí/nezaškrtnutí
  - ✅ **Dokončeno** - Úsp<PERSON>šn<PERSON> otestováno
  - ❌ **Problém** - Selhalo nebo byl nalezen problém
  - ⚠️ **Přeskočeno** - Nedostupné nebo nelze testovat
  - ⏳ **Čeká** - Zatím neotestováno
- **Výhoda**: Reálnější zachycení stavu testování

#### 📝 Rozšířené poznámky
- **Nová funkce**: Individuální poznámky k jednotlivým bodům
- **Nová funkce**: Obecná poznámka k celému checklistu
- **UI**: Textová pole přímo u každého bodu

#### 💾 Částečné uložení
- **Nová funkce**: Možnost uložit nedokončený checklist
- **Tlačítka**: "Uložit" (částečně) vs "Dokončit" (kompletně)
- **Výhoda**: Můžete pokračovat v práci později

#### ⚙️ Správa checklistu
- **Nová sekce**: "Správa checklistu" v navigaci
- **Funkce**: Přidávání vlastních kontrolních bodů
- **Funkce**: Editace a mazání vlastních bodů
- **Funkce**: Ukládání jako šablona

### 🔄 Změny workflow

#### Starý workflow:
1. Úvod → Zadání jména → Složka → Odstávky → Výběr → Dashboard → Checklist

#### Nový workflow:
1. **Login** → **Odstávky** → **Výběr odstávky** → **Automaticky checklist**

### 🎨 UI/UX vylepšení
- Moderní login stránka s OTE branding
- Karty místo jednoduchých řádků pro kontrolní body
- Barevné rozlišení stavů (zelená/červená/žlutá/šedá)
- Chip komponenty pro zobrazení stavů
- Lepší rozložení tlačítek (Uložit/Dokončit/Export)

## Verze 1.1.0 - Zjednodušení a opravy (2025-01-27)

### ✅ Hlavní opravy a vylepšení

#### 🔧 Odstranění zbytečného výběru složky
- **Problém**: Aplikace vyžadovala výběr složky pro ukládání dat, což bylo matoucí a zbytečné
- **Řešení**: Odstraněn požadavek na výběr složky, data se ukládají lokálně v prohlížeči
- **Výhody**: Jednodušší workflow, rychlejší start aplikace

#### 📋 Aktualizace checklist obsahu
- **Problém**: Checklist neobsahoval správné kontrolní body pro OTE odstávky
- **Řešení**: Implementovány všechny 15 standardních kontrolních bodů podle specifikace
- **Nový obsah**:
  1. Kontrola přihlašovací stránky OTE market (ote-cr.cz)
  2. Interní test portálu
  3. Test podpisových certifikátů (DT, OTE-COM)
  4. Kontrola DT + AMQP (mailová fronta)
  5. OTE-COM (elektřina, plyn)
  6. Mobilní aplikace (POZE, VDT, VDP)
  7. SAP (CDP, KSM, KSP, OZP)
  8. Kontakt N4T4GAS (pí. Lerbleiterová 604 222 872)
  9. KSP - kontrola nominací
  10. PMB - kontrola statusů
  11. Nastavení vývěsky o ukončení odstávky
  12. Aktualizace informací o odstávce
  13. Testovací zpráva 621 (<EMAIL>)
  14. Ověření EZP portálu
  15. E-mail o ukončení odstávky

#### 🎯 Zlepšení uživatelského workflow
- **Problém**: Checklist se nezobrazoval po výběru odstávky
- **Řešení**: Opravena navigace a stav aplikace
- **Nový workflow**:
  1. Zadání jména (bez složky)
  2. Automatický přechod na odstávky
  3. Výběr odstávky ze seznamu
  4. Automatické zobrazení checklistu
  5. Dokončení a export

#### 💾 Zjednodušení ukládání dat
- **Změna**: Všechna data se ukládají lokálně v LocalStorage
- **Export**: Soubory se stahují přímo do Downloads složky
- **Formáty**: PDF, JSON, Excel, CSV

### 🗑️ Odstraněné funkce
- Výběr složky pro ukládání dat
- File System Access API fallback mechanismus
- Složitá kompatibilita s různými prohlížeči
- Nepotřebné komponenty (FolderSupportInfo, folderUtils)

### 🔄 Technické změny
- Odstraněn `folder` stav z aplikačního kontextu
- Zjednodušeny komponenty WelcomeCard a EnhancedWelcomeCard
- Aktualizovány export a storage služby
- Opravena navigace mezi sekcemi
- Vyčištěny nepotřebné importy a závislosti

### 📱 Uživatelské rozhraní
- Zjednodušený úvodní formulář (pouze jméno)
- Automatické zobrazení checklistu po výběru odstávky
- Lepší indikátory stavu v navigaci
- Konzistentnější UX napříč aplikací

### 🎯 Výsledek
Aplikace je nyní jednodušší na použití, má správný obsah checklistu a funguje podle očekávaného workflow:
**Jméno → Odstávky → Výběr odstávky → Checklist → Export**

---

## Verze 1.0.0 - Počáteční verze
- Základní funkcionalita aplikace
- React + Material-UI
- LocalStorage pro persistenci dat
- Export do PDF/JSON/Excel
- Správa odstávek a checklistů
