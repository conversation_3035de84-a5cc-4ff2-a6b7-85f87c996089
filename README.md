# Evidence checklistů odstávek OTE

Moderní React aplikace pro evidenci a správu checklistů při odstávkách systému OTE.

## Funkce

- ✅ **Jednoduchý login** - <PERSON><PERSON><PERSON> přihlášení pouze se jménem
- ✅ **Správa odstávek** - <PERSON><PERSON><PERSON><PERSON><PERSON>, editace a mazání plánovaných odstávek
- ✅ **Flexibilní checklist** - 4 stavy pro každý kontrolní bod (dokončeno/problém/přeskočeno/čeká)
- ✅ **Automatické přesměrování** - Po výběru odstávky okamžitě na checklist
- ✅ **Poznámky** - Individuální poznámky k bodům i celému checklistu
- ✅ **Částečné uložení** - Možnost uložit nedokončený checklist
- ✅ **Historie kontrol** - Přehled všech dokončených i částečných checklistů
- ✅ **Export dat** - JSON, PDF a Excel formáty
- ✅ **Správa checklistu** - Přidávání vlastních kontrolních bodů
- ✅ **Persistentní úložiště** - Automatické ukládání dat do LocalStorage
- ✅ **Responsivní design** - Optimalizováno pro desktop i mobil

## Technologie

- **React 19** - moderní UI framework
- **Material-UI 7** - komponenty a design systém
- **Vite 7** - rychlý build tool
- **LocalStorage** - persistentní úložiště dat
- **jsPDF & xlsx** - export do PDF a Excel

## Spuštění

```bash
npm install
npm run dev
```

## Jak používat

1. **Přihlášení** - Zadejte své jméno na login stránce
2. **Vyberte odstávku** - Ze seznamu odstávek vyberte tu, kterou chcete kontrolovat
3. **Automatické přesměrování** - Po výběru odstávky se automaticky zobrazí checklist
4. **Flexibilní kontrola** - Pro každý bod můžete nastavit stav:
   - ✅ **Dokončeno** - Úspěšně otestováno
   - ❌ **Problém** - Selhalo nebo byl nalezen problém
   - ⚠️ **Přeskočeno** - Nedostupné nebo nelze testovat
   - ⏳ **Čeká** - Zatím neotestováno
5. **Poznámky** - K jednotlivým bodům i celému checklistu můžete přidat poznámky
6. **Uložení** - Můžete uložit částečně dokončený checklist a pokračovat později
7. **Export** - Dokončené checklisty exportujte do PDF nebo JSON

## Kontrolní body checklistu

Aplikace obsahuje 15 standardních kontrolních bodů pro odstávky OTE:

1. Kontrola přihlašovací stránky OTE market (ote-cr.cz)
2. Interní test portálu
3. Test podpisových certifikátů (DT, OTE-COM)
4. Kontrola DT + AMQP (mailová fronta)
5. OTE-COM (elektřina, plyn)
6. Mobilní aplikace (POZE, VDT, VDP)
7. SAP (CDP, KSM, KSP, OZP)
8. Kontakt N4T4GAS (pí. Lerbleiterová 604 222 872)
9. KSP - kontrola nominací
10. PMB - kontrola statusů
11. Nastavení vývěsky o ukončení odstávky
12. Aktualizace informací o odstávce
13. Testovací zpráva 621 (<EMAIL>)
14. Ověření EZP portálu
15. E-mail o ukončení odstávky

## Struktura projektu

```
src/
├── components/          # Znovupoužitelné komponenty
├── contexts/           # React Context pro globální stav
├── hooks/              # Custom React hooks
├── services/           # Služby pro práci s daty
├── utils/              # Pomocné funkce
└── data/               # Datové soubory a konstanty
```
