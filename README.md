# Evidence checklistů odstávek OTE

Moderní React aplikace pro evidenci a správu checklistů při odstávkách systému OTE.

## Funkce

- ✅ **Persistentní login** - Pamatuje si přihlášení i po obnovení stránky
- ✅ **Multi-user systém** - Více lidí může kontrolovat stejnou odstávku
- ✅ **Flexibilní checklist** - 4 stavy pro každý kontrolní bod
- ✅ **Automatické přesměrování** - Po výběru odstávky okamžitě na checklist
- ✅ **Sdílené checklisty** - Vazba checklist ↔ odstávka ↔ uživatel
- ✅ **Poznámky** - Individuální poznámky k bodům i celému checklistu
- ✅ **Částečné uložení** - Možnost uložit nedokončený checklist
- ✅ **Historie a statistiky** - P<PERSON>ehled všech checklistů pro každou odstávku
- ✅ **JSON databáze** - Strukturované ukládání dat podle odstávek a uživatelů
- ✅ **Export dat** - JSON, PDF a Excel formáty
- ✅ **Správa checklistu** - Zobrazení aktuální šablony a možnost rozšíření
- ✅ **Responsivní design** - Optimalizováno pro desktop i mobil

## Technologie

- **React 19** - moderní UI framework
- **Material-UI 7** - komponenty a design systém
- **Vite 7** - rychlý build tool
- **LocalStorage** - persistentní úložiště dat
- **jsPDF & xlsx** - export do PDF a Excel

## Spuštění

```bash
npm install
npm run dev
```

## Jak používat

### 🔐 **Přihlášení a persistentní session**
- Zadejte své jméno na login stránce
- **Aplikace si pamatuje přihlášení** i po obnovení stránky
- Logout tlačítko v pravém horním rohu

### 📋 **Multi-user checklist systém**
1. **Vyberte odstávku** - Ze seznamu odstávek (např. 22.07.2025)
2. **Automatické přesměrování** - Okamžitě se zobrazí checklist
3. **Sdílené checklisty** - Více lidí může kontrolovat stejnou odstávku
4. **Zobrazení ostatních** - Vidíte, kdo další pracuje na stejné odstávce

### 🎯 **Flexibilní kontrola**
Pro každý bod můžete nastavit stav:
- ✅ **Dokončeno** - Úspěšně otestováno
- ❌ **Problém** - Selhalo nebo byl nalezen problém
- ⚠️ **Přeskočeno** - Nedostupné nebo nelze testovat
- ⏳ **Čeká** - Zatím neotestováno

### 📝 **Poznámky a uložení**
- **Individuální poznámky** k jednotlivým bodům
- **Obecná poznámka** k celému checklistu
- **Částečné uložení** - pokračujte v práci později
- **Automatické načítání** existujících checklistů

### 📊 **Historie a statistiky**
- **Historie checklistů** pro každou odstávku
- **Statistiky** - kolik lidí kontrolovalo, kolik dokončilo
- **Tlačítko historie** u každé odstávky se statistikami
- **Detailní přehled** všech kontrolních bodů a poznámek

### ⚙️ **Správa checklistu**
- **Zobrazení aktuální šablony** se všemi 15 body
- **Přidávání vlastních bodů** (v budoucnu)
- **Editace šablony** (v budoucnu)

## Kontrolní body checklistu

Aplikace obsahuje 15 standardních kontrolních bodů pro odstávky OTE:

1. Kontrola přihlašovací stránky OTE market (ote-cr.cz)
2. Interní test portálu
3. Test podpisových certifikátů (DT, OTE-COM)
4. Kontrola DT + AMQP (mailová fronta)
5. OTE-COM (elektřina, plyn)
6. Mobilní aplikace (POZE, VDT, VDP)
7. SAP (CDP, KSM, KSP, OZP)
8. Kontakt N4T4GAS (pí. Lerbleiterová 604 222 872)
9. KSP - kontrola nominací
10. PMB - kontrola statusů
11. Nastavení vývěsky o ukončení odstávky
12. Aktualizace informací o odstávce
13. Testovací zpráva 621 (<EMAIL>)
14. Ověření EZP portálu
15. E-mail o ukončení odstávky

## Struktura projektu

```
src/
├── components/          # Znovupoužitelné komponenty
├── contexts/           # React Context pro globální stav
├── hooks/              # Custom React hooks
├── services/           # Služby pro práci s daty
├── utils/              # Pomocné funkce
└── data/               # Datové soubory a konstanty
```
