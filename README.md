# Evidence checklistů odstávek OTE

Moderní React aplikace pro evidenci a správu checklistů při odstávkách systému OTE.

## Funkce

- ✅ **Správa odstávek** - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, editace a ma<PERSON><PERSON><PERSON> plánovaných odstávek
- ✅ **Interaktivní checklist** - 15 kontrolních bodů s detailními popisy
- ✅ **Persistentní <PERSON>** - automatické ukládání dat do LocalStorage
- ✅ **Historie kontrol** - přehled všech dokončených checklistů
- ✅ **Export dat** - JSON, PDF a Excel formáty
- ✅ **Tmavý/světlý režim** - přepínání vzhledu aplikace
- ✅ **Responsivní design** - optimalizováno pro desktop i mobil
- ✅ **Pokročilé notifikace** - systém zpětné vazby pro uživatele
- ✅ **Vlastní checklist položky** - možnost přidávat a upravovat body
- ✅ **Šablony checklistů** - různé typy pro různé odstávky

## Technologie

- **React 19** - moderní UI framework
- **Material-UI 7** - komponenty a design systém
- **Vite 7** - rychlý build tool
- **LocalStorage** - persistentní úložiště dat
- **jsPDF & xlsx** - export do PDF a Excel

## Spuštění

```bash
npm install
npm run dev
```

## Jak používat

1. **Zadejte jméno** - Na úvodní stránce zadejte své jméno
2. **Přejděte na Odstávky** - Vyberte nebo přidejte odstávku ze seznamu
3. **Spusťte Checklist** - Po výběru odstávky se automaticky zobrazí checklist s 15 kontrolními body
4. **Dokončete kontrolu** - Zaškrtněte všechny body a přidejte poznámky
5. **Exportujte výsledky** - Výsledky se automaticky uloží a můžete je exportovat do PDF/JSON

## Kontrolní body checklistu

Aplikace obsahuje 15 standardních kontrolních bodů pro odstávky OTE:

1. Kontrola přihlašovací stránky OTE market (ote-cr.cz)
2. Interní test portálu
3. Test podpisových certifikátů (DT, OTE-COM)
4. Kontrola DT + AMQP (mailová fronta)
5. OTE-COM (elektřina, plyn)
6. Mobilní aplikace (POZE, VDT, VDP)
7. SAP (CDP, KSM, KSP, OZP)
8. Kontakt N4T4GAS (pí. Lerbleiterová 604 222 872)
9. KSP - kontrola nominací
10. PMB - kontrola statusů
11. Nastavení vývěsky o ukončení odstávky
12. Aktualizace informací o odstávce
13. Testovací zpráva 621 (<EMAIL>)
14. Ověření EZP portálu
15. E-mail o ukončení odstávky

## Struktura projektu

```
src/
├── components/          # Znovupoužitelné komponenty
├── contexts/           # React Context pro globální stav
├── hooks/              # Custom React hooks
├── services/           # Služby pro práci s daty
├── utils/              # Pomocné funkce
└── data/               # Datové soubory a konstanty
```
