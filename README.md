# Evidence checklistů odstávek OTE

Moderní React aplikace pro evidenci a správu checklistů při odstávkách systému OTE.

## Funkce

- ✅ **Správa odstávek** - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, editace a ma<PERSON><PERSON><PERSON> plánovaných odstávek
- ✅ **Interaktivní checklist** - 15 kontrolních bodů s detailními popisy
- ✅ **Persistentní <PERSON>** - automatické ukládání dat do LocalStorage
- ✅ **Historie kontrol** - přehled všech dokončených checklistů
- ✅ **Export dat** - JSON, PDF a Excel formáty
- ✅ **Tmavý/světlý režim** - přepínání vzhledu aplikace
- ✅ **Responsivní design** - optimalizováno pro desktop i mobil
- ✅ **Pokročilé notifikace** - systém zpětné vazby pro uživatele
- ✅ **Vlastní checklist položky** - možnost přidávat a upravovat body
- ✅ **Šablony checklistů** - různé typy pro různé odstávky

## Technologie

- **React 19** - moderní UI framework
- **Material-UI 7** - komponenty a design systém
- **Vite 7** - rychlý build tool
- **LocalStorage** - persistentní úložiště dat
- **jsPDF & xlsx** - export do PDF a Excel

## Spuštění

```bash
npm install
npm run dev
```

## Kompatibilita prohlížečů

Aplikace je navržena pro maximální kompatibilitu s moderními prohlížeči:

- ✅ **Chrome 86+** - plná podpora (File System Access API)
- ✅ **Edge 86+** - plná podpora (File System Access API)
- ✅ **Firefox 50+** - kompatibilní (webkitdirectory fallback)
- ✅ **Safari 11.1+** - kompatibilní (webkitdirectory fallback)
- ❌ **Internet Explorer** - nepodporován

Aplikace automaticky detekuje podporu výběru složky a použije nejvhodnější metodu pro váš prohlížeč. Více informací v [BROWSER_COMPATIBILITY.md](./BROWSER_COMPATIBILITY.md).

## Struktura projektu

```
src/
├── components/          # Znovupoužitelné komponenty
├── contexts/           # React Context pro globální stav
├── hooks/              # Custom React hooks
├── services/           # Služby pro práci s daty
├── utils/              # Pomocné funkce
└── data/               # Datové soubory a konstanty
```
