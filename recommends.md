# Doporučení pro zlepšení kódu a UX/UI

## Analýza hlavních komponent

### 1. App.jsx
- **Oddělit logiku navigace a layoutu** (<PERSON><PERSON>, App<PERSON><PERSON>, hlav<PERSON><PERSON> obsah jako samostatné komponenty)
- **<PERSON><PERSON><PERSON> 'checklist' a 'history' používají Dashboard** – zvážit parametrizaci nebo samostatné komponenty
- **Props forwarding** – předávat potřebné props (user, setUser, folder, setFolder) do podkomponent
- **Přístupnost** – přidat ARIA popisky k navigačním prvkům a ikonám
- **Responsivita** – otestovat sidebar a layout na mobilních zařízeních

### 2. Checklist.jsx
- **Oddělit data od komponenty** (checklist body do samostatného souboru)
- **Přístupnost** – použít checkboxy místo buttonů
- **Možnost rozšíření** – umožnit dynamické přid<PERSON>í bodů
- **Zpětná vazba** – po dokončení checklistu zobrazit vizuální potvrzení

### 3. Dashboard.jsx
- **Duplicitní data** – sjednotit zdroj checklist dat
- **Kompozice** – rozdělit Dashboard na menší komponenty
- **Stav** – zvážit globální stav pro sdílení vybrané odstávky
- **UI/UX** – zvýraznit vybranou položku v seznamu

### 4. FolderSelector.jsx
- **Material UI** – použít MUI Button místo nativního
- **Chybové stavy** – lepší zpětná vazba (snackbar místo alertu)
- **Přístupnost** – přidat popisek tlačítka

### 5. OutageList.jsx
- **Validace** – přidat validaci vstupů (datum, čas)
- **UX** – po přidání nové odstávky automaticky vybrat novou položku
- **Modularita** – oddělit formulář pro přidání odstávky
- **Přístupnost** – tabulka lépe přístupná (aria-labels, role)

### 6. UserInput.jsx
- **Material UI** – použít MUI TextField a Button
- **Validace** – zobrazit chybovou zprávu při prázdném vstupu
- **Přístupnost** – přidat label a aria popisky

### 7. WelcomeCard.jsx
- **Props** – ověřit správné předávání setFolder a setUser
- **UX** – po úspěšném nastavení složky a jména automaticky přesměrovat do sekce Odstávky
- **Přístupnost** – přidat role a aria popisky

---

## Obecná doporučení

1. **Konzistence UI** – všude používat Material UI komponenty
2. **Přístupnost** – přidat ARIA popisky, role, ovladatelnost klávesnicí
3. **Modularita** – rozdělit větší komponenty na menší, znovupoužitelné části
4. **Stavová logika** – zvážit Context API nebo state management knihovnu
5. **Validace** – přidat validace vstupů a lepší zpětnou vazbu
6. **Responsivita** – otestovat a upravit layout pro mobilní zařízení
7. **Dokumentace** – doplnit komentáře a README k jednotlivým komponentám 