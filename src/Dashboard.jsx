import React, { useState } from 'react';
import { Box, Tooltip } from '@mui/material';
import OutageList from './OutageList';
import Checklist from './Checklist';

function OutageSelector({ onSelect }) {
  return <OutageList onOutageSelected={onSelect} />;
}

function OutageChecklist({ outage }) {
  if (!outage) {
    return (
      <Tooltip title="Vyberte odstávku vlevo pro zobrazení checklistu" arrow>
        <Box sx={{ color: 'text.secondary', mt: 8, textAlign: 'center', fontSize: 22, p: 3, borderRadius: 3, background: '#f7fafd', boxShadow: 2 }}>
          Vyberte odstávku vlevo pro zobrazení checklistu.
        </Box>
      </Tooltip>
    );
  }
  return <Checklist outage={outage} />;
}

function Dashboard() {
  const [selectedOutage, setSelectedOutage] = useState(null);

  return (
    <Box sx={{
      display: 'flex',
      flexDirection: { xs: 'column', md: 'row' },
      alignItems: { xs: 'stretch', md: 'flex-start' },
      mt: { xs: 1, md: 2 },
      mb: { xs: 2, md: 6 },
      width: '100%',
      maxWidth: 1400,
      mx: 'auto',
      gap: { xs: 4, md: 6 },
      px: { xs: 1, md: 0 },
    }}>
      <Box sx={{ flex: 1, minWidth: 260, mb: { xs: 4, md: 0 } }}>
        <OutageSelector onSelect={setSelectedOutage} />
      </Box>
      <Box sx={{ flex: 2, minWidth: 380 }}>
        <OutageChecklist outage={selectedOutage} />
      </Box>
    </Box>
  );
}

export default Dashboard; 