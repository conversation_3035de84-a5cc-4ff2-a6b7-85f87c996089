// Služba pro práci s LocalStorage
class StorageService {
  constructor() {
    this.keys = {
      APP_STATE: 'ote_checklist_app_state',
      OUTAGES: 'ote_checklist_outages',
      CHECKLIST_HISTORY: 'ote_checklist_history',
      USER_SETTINGS: 'ote_checklist_settings',
      CUSTOM_CHECKLIST_ITEMS: 'ote_checklist_custom_items'
    }
  }

  // Uložení celého stavu aplikace
  saveAppState(state) {
    try {
      const dataToSave = {
        user: state.user,
        outages: state.outages,
        checklistHistory: state.checklistHistory,
        settings: state.settings,
        timestamp: new Date().toISOString()
      }
      localStorage.setItem(this.keys.APP_STATE, JSON.stringify(dataToSave))
      return true
    } catch (error) {
      console.error('Chyba při ukládání stavu aplikace:', error)
      return false
    }
  }

  // Načtení stavu aplikace
  loadAppState() {
    try {
      const saved = localStorage.getItem(this.keys.APP_STATE)
      if (saved) {
        const data = JSON.parse(saved)
        // Kontrola stáří dat (max 30 dní)
        const savedDate = new Date(data.timestamp)
        const now = new Date()
        const daysDiff = (now - savedDate) / (1000 * 60 * 60 * 24)
        
        if (daysDiff > 30) {
          console.warn('Uložená data jsou starší než 30 dní, budou smazána')
          this.clearAppState()
          return null
        }
        
        return data
      }
      return null
    } catch (error) {
      console.error('Chyba při načítání stavu aplikace:', error)
      return null
    }
  }

  // Uložení odstávek
  saveOutages(outages) {
    try {
      localStorage.setItem(this.keys.OUTAGES, JSON.stringify(outages))
      return true
    } catch (error) {
      console.error('Chyba při ukládání odstávek:', error)
      return false
    }
  }

  // Načtení odstávek
  loadOutages() {
    try {
      const saved = localStorage.getItem(this.keys.OUTAGES)
      return saved ? JSON.parse(saved) : []
    } catch (error) {
      console.error('Chyba při načítání odstávek:', error)
      return []
    }
  }

  // Uložení historie checklistů
  saveChecklistHistory(history) {
    try {
      localStorage.setItem(this.keys.CHECKLIST_HISTORY, JSON.stringify(history))
      return true
    } catch (error) {
      console.error('Chyba při ukládání historie:', error)
      return false
    }
  }

  // Načtení historie checklistů
  loadChecklistHistory() {
    try {
      const saved = localStorage.getItem(this.keys.CHECKLIST_HISTORY)
      return saved ? JSON.parse(saved) : []
    } catch (error) {
      console.error('Chyba při načítání historie:', error)
      return []
    }
  }

  // Uložení nastavení
  saveSettings(settings) {
    try {
      localStorage.setItem(this.keys.USER_SETTINGS, JSON.stringify(settings))
      return true
    } catch (error) {
      console.error('Chyba při ukládání nastavení:', error)
      return false
    }
  }

  // Načtení nastavení
  loadSettings() {
    try {
      const saved = localStorage.getItem(this.keys.USER_SETTINGS)
      return saved ? JSON.parse(saved) : null
    } catch (error) {
      console.error('Chyba při načítání nastavení:', error)
      return null
    }
  }

  // Uložení vlastních checklist položek
  saveCustomChecklistItems(items) {
    try {
      localStorage.setItem(this.keys.CUSTOM_CHECKLIST_ITEMS, JSON.stringify(items))
      return true
    } catch (error) {
      console.error('Chyba při ukládání vlastních položek:', error)
      return false
    }
  }

  // Načtení vlastních checklist položek
  loadCustomChecklistItems() {
    try {
      const saved = localStorage.getItem(this.keys.CUSTOM_CHECKLIST_ITEMS)
      return saved ? JSON.parse(saved) : []
    } catch (error) {
      console.error('Chyba při načítání vlastních položek:', error)
      return []
    }
  }

  // Vymazání všech dat
  clearAppState() {
    try {
      Object.values(this.keys).forEach(key => {
        localStorage.removeItem(key)
      })
      return true
    } catch (error) {
      console.error('Chyba při mazání dat:', error)
      return false
    }
  }

  // Získání velikosti uložených dat
  getStorageSize() {
    try {
      let totalSize = 0
      Object.values(this.keys).forEach(key => {
        const item = localStorage.getItem(key)
        if (item) {
          totalSize += item.length
        }
      })
      return {
        bytes: totalSize,
        kb: Math.round(totalSize / 1024 * 100) / 100,
        mb: Math.round(totalSize / (1024 * 1024) * 100) / 100
      }
    } catch (error) {
      console.error('Chyba při výpočtu velikosti:', error)
      return { bytes: 0, kb: 0, mb: 0 }
    }
  }

  // Export všech dat
  exportAllData() {
    try {
      const data = {
        appState: this.loadAppState(),
        outages: this.loadOutages(),
        checklistHistory: this.loadChecklistHistory(),
        settings: this.loadSettings(),
        customItems: this.loadCustomChecklistItems(),
        exportDate: new Date().toISOString(),
        version: '1.0'
      }
      return data
    } catch (error) {
      console.error('Chyba při exportu dat:', error)
      return null
    }
  }

  // Import všech dat
  importAllData(data) {
    try {
      if (data.appState) {
        localStorage.setItem(this.keys.APP_STATE, JSON.stringify(data.appState))
      }
      if (data.outages) {
        this.saveOutages(data.outages)
      }
      if (data.checklistHistory) {
        this.saveChecklistHistory(data.checklistHistory)
      }
      if (data.settings) {
        this.saveSettings(data.settings)
      }
      if (data.customItems) {
        this.saveCustomChecklistItems(data.customItems)
      }
      return true
    } catch (error) {
      console.error('Chyba při importu dat:', error)
      return false
    }
  }
}

// Singleton instance
export const storageService = new StorageService()
