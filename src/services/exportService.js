// Služba pro export dat do různých formátů

class ExportService {
  // Export do JSON
  exportToJSON(data, filename = 'ote-checklist-export') {
    try {
      const jsonString = JSON.stringify(data, null, 2)
      const blob = new Blob([jsonString], { type: 'application/json' })
      this.downloadFile(blob, `${filename}.json`)
      return true
    } catch (error) {
      console.error('Chyba při exportu do JSON:', error)
      return false
    }
  }

  // Export checklistu do PDF (vyžaduje jsPDF)
  async exportChecklistToPDF(checklistData, outage, user) {
    try {
      // Dynamický import jsPDF
      const { jsPDF } = await import('jspdf')
      
      const doc = new jsPDF()
      const pageWidth = doc.internal.pageSize.width
      const margin = 20
      let yPosition = margin

      // Hlavička
      doc.setFontSize(20)
      doc.setFont(undefined, 'bold')
      doc.text('OTE - Checklist odstávky', margin, yPosition)
      yPosition += 15

      // Informace o odstávce
      doc.setFontSize(12)
      doc.setFont(undefined, 'normal')
      if (outage) {
        doc.text(`Datum: ${outage.date}`, margin, yPosition)
        yPosition += 8
        doc.text(`Čas: ${outage.from} - ${outage.to}`, margin, yPosition)
        yPosition += 8
        doc.text(`Poznámka: ${outage.note}`, margin, yPosition)
        yPosition += 8
      }
      
      if (user) {
        doc.text(`Kontroloval: ${user}`, margin, yPosition)
        yPosition += 8
      }

      doc.text(`Datum kontroly: ${new Date().toLocaleDateString('cs-CZ')}`, margin, yPosition)
      yPosition += 15

      // Checklist položky
      doc.setFontSize(14)
      doc.setFont(undefined, 'bold')
      doc.text('Kontrolní body:', margin, yPosition)
      yPosition += 10

      doc.setFontSize(10)
      doc.setFont(undefined, 'normal')

      checklistData.checklist.forEach((item, index) => {
        // Kontrola, zda se vejdeme na stránku
        if (yPosition > 250) {
          doc.addPage()
          yPosition = margin
        }

        const checkbox = item.checked ? '☑' : '☐'
        const text = `${checkbox} ${item.title}`
        
        doc.text(text, margin, yPosition)
        yPosition += 6

        if (item.note && item.note.trim()) {
          doc.setFont(undefined, 'italic')
          doc.text(`   ${item.note}`, margin + 5, yPosition)
          doc.setFont(undefined, 'normal')
          yPosition += 6
        }
        yPosition += 2
      })

      // Poznámky
      if (checklistData.note && checklistData.note.trim()) {
        yPosition += 10
        doc.setFontSize(12)
        doc.setFont(undefined, 'bold')
        doc.text('Poznámky:', margin, yPosition)
        yPosition += 8
        
        doc.setFontSize(10)
        doc.setFont(undefined, 'normal')
        const noteLines = doc.splitTextToSize(checklistData.note, pageWidth - 2 * margin)
        doc.text(noteLines, margin, yPosition)
      }

      // Uložení
      const filename = `ote-checklist-${new Date().toISOString().slice(0, 10)}.pdf`
      doc.save(filename)
      return true
    } catch (error) {
      console.error('Chyba při exportu do PDF:', error)
      return false
    }
  }

  // Export do Excel (vyžaduje xlsx)
  async exportToExcel(data, filename = 'ote-checklist-export') {
    try {
      // Dynamický import xlsx
      const XLSX = await import('xlsx')
      
      const workbook = XLSX.utils.book_new()

      // List s odstávkami
      if (data.outages && data.outages.length > 0) {
        const outagesSheet = XLSX.utils.json_to_sheet(data.outages)
        XLSX.utils.book_append_sheet(workbook, outagesSheet, 'Odstávky')
      }

      // List s historií checklistů
      if (data.checklistHistory && data.checklistHistory.length > 0) {
        const historyData = data.checklistHistory.map(record => ({
          'Datum dokončení': new Date(record.completedAt).toLocaleDateString('cs-CZ'),
          'Čas dokončení': new Date(record.completedAt).toLocaleTimeString('cs-CZ'),
          'Odstávka': record.outage ? `${record.outage.date} ${record.outage.from}-${record.outage.to}` : 'N/A',
          'Uživatel': record.user || 'N/A',
          'Dokončené body': record.checklist ? record.checklist.filter(item => item.checked).length : 0,
          'Celkem bodů': record.checklist ? record.checklist.length : 0,
          'Poznámky': record.note || ''
        }))
        
        const historySheet = XLSX.utils.json_to_sheet(historyData)
        XLSX.utils.book_append_sheet(workbook, historySheet, 'Historie')
      }

      // List s detaily posledního checklistu
      if (data.checklistHistory && data.checklistHistory.length > 0) {
        const lastChecklist = data.checklistHistory[data.checklistHistory.length - 1]
        if (lastChecklist.checklist) {
          const checklistData = lastChecklist.checklist.map(item => ({
            'ID': item.id,
            'Název': item.title,
            'Dokončeno': item.checked ? 'Ano' : 'Ne',
            'Detail': item.note || ''
          }))
          
          const checklistSheet = XLSX.utils.json_to_sheet(checklistData)
          XLSX.utils.book_append_sheet(workbook, checklistSheet, 'Poslední checklist')
        }
      }

      // Uložení souboru
      XLSX.writeFile(workbook, `${filename}.xlsx`)
      return true
    } catch (error) {
      console.error('Chyba při exportu do Excel:', error)
      return false
    }
  }

  // Export historie do CSV
  exportHistoryToCSV(history, filename = 'ote-checklist-historie') {
    try {
      const headers = [
        'Datum dokončení',
        'Čas dokončení', 
        'Odstávka',
        'Uživatel',
        'Dokončené body',
        'Celkem bodů',
        'Poznámky'
      ]

      const csvData = history.map(record => [
        new Date(record.completedAt).toLocaleDateString('cs-CZ'),
        new Date(record.completedAt).toLocaleTimeString('cs-CZ'),
        record.outage ? `${record.outage.date} ${record.outage.from}-${record.outage.to}` : 'N/A',
        record.user || 'N/A',
        record.checklist ? record.checklist.filter(item => item.checked).length : 0,
        record.checklist ? record.checklist.length : 0,
        record.note || ''
      ])

      const csvContent = [headers, ...csvData]
        .map(row => row.map(field => `"${field}"`).join(','))
        .join('\n')

      const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
      this.downloadFile(blob, `${filename}.csv`)
      return true
    } catch (error) {
      console.error('Chyba při exportu do CSV:', error)
      return false
    }
  }

  // Pomocná funkce pro stažení souboru
  downloadFile(blob, filename) {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }

  // Získání formátovaných dat pro export
  prepareDataForExport(appState) {
    return {
      metadata: {
        exportDate: new Date().toISOString(),
        version: '1.0',
        user: appState.user
      },
      outages: appState.outages || [],
      checklistHistory: appState.checklistHistory || [],
      settings: appState.settings || {}
    }
  }
}

// Singleton instance
export const exportService = new ExportService()
