/**
 * Služba pro správu checklistů podle odstávek a uživatelů
 * Struktura dat: outageId -> userId -> checklist data
 */

const STORAGE_KEY = 'ote-checklist-data';

class ChecklistService {
  constructor() {
    this.data = this.loadData();
  }

  /**
   * Načte data z localStorage
   */
  loadData() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      console.error('Chyba při načítání dat checklistu:', error);
      return {};
    }
  }

  /**
   * Uloží data do localStorage
   */
  saveData() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.data));
    } catch (error) {
      console.error('Chyba při ukládání dat checklistu:', error);
    }
  }

  /**
   * Získá checklist pro konkrétní odstávku a uživatele
   * @param {string} outageId - ID odstávky
   * @param {string} userId - Jméno uživatele
   * @returns {object|null} - Checklist data nebo null
   */
  getChecklist(outageId, userId) {
    if (!this.data[outageId] || !this.data[outageId][userId]) {
      return null;
    }
    return this.data[outageId][userId];
  }

  /**
   * Uloží checklist pro konkrétní odstávku a uživatele
   * @param {string} outageId - ID odstávky
   * @param {string} userId - Jméno uživatele
   * @param {object} checklistData - Data checklistu
   */
  saveChecklist(outageId, userId, checklistData) {
    if (!this.data[outageId]) {
      this.data[outageId] = {};
    }
    
    this.data[outageId][userId] = {
      ...checklistData,
      lastModified: new Date().toISOString(),
      userId,
      outageId
    };
    
    this.saveData();
  }

  /**
   * Získá všechny checklisty pro konkrétní odstávku (všichni uživatelé)
   * @param {string} outageId - ID odstávky
   * @returns {object} - Objekt s checklisty všech uživatelů
   */
  getOutageChecklists(outageId) {
    return this.data[outageId] || {};
  }

  /**
   * Získá seznam všech uživatelů, kteří pracovali na konkrétní odstávce
   * @param {string} outageId - ID odstávky
   * @returns {array} - Seznam jmen uživatelů
   */
  getOutageUsers(outageId) {
    const outageData = this.data[outageId];
    return outageData ? Object.keys(outageData) : [];
  }

  /**
   * Získá statistiky pro odstávku
   * @param {string} outageId - ID odstávky
   * @returns {object} - Statistiky
   */
  getOutageStats(outageId) {
    const checklists = this.getOutageChecklists(outageId);
    const users = Object.keys(checklists);
    
    if (users.length === 0) {
      return {
        totalUsers: 0,
        completedUsers: 0,
        lastActivity: null
      };
    }

    const completedUsers = users.filter(userId => 
      checklists[userId].isComplete
    ).length;

    const lastActivity = Math.max(
      ...users.map(userId => new Date(checklists[userId].lastModified).getTime())
    );

    return {
      totalUsers: users.length,
      completedUsers,
      lastActivity: new Date(lastActivity).toISOString()
    };
  }

  /**
   * Smaže checklist konkrétního uživatele pro odstávku
   * @param {string} outageId - ID odstávky
   * @param {string} userId - Jméno uživatele
   */
  deleteChecklist(outageId, userId) {
    if (this.data[outageId] && this.data[outageId][userId]) {
      delete this.data[outageId][userId];
      
      // Pokud už nejsou žádní uživatelé, smaž celou odstávku
      if (Object.keys(this.data[outageId]).length === 0) {
        delete this.data[outageId];
      }
      
      this.saveData();
    }
  }

  /**
   * Exportuje všechna data do JSON
   * @returns {object} - Všechna data
   */
  exportAllData() {
    return {
      exportDate: new Date().toISOString(),
      version: '2.0',
      data: this.data
    };
  }

  /**
   * Importuje data z JSON
   * @param {object} importData - Data k importu
   */
  importData(importData) {
    if (importData.data) {
      this.data = { ...this.data, ...importData.data };
      this.saveData();
    }
  }

  /**
   * Vyčistí všechna data
   */
  clearAllData() {
    this.data = {};
    this.saveData();
  }
}

// Singleton instance
export const checklistService = new ChecklistService();
