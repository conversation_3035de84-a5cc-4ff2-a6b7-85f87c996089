#root {
  max-width: 1440px;
  width: 95vw;
  margin: 0 auto;
  padding: 2.5rem 2rem 2rem 2rem;
  text-align: center;
  min-height: 100vh;
  box-sizing: border-box;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

@media (max-width: 1200px) {
  #root {
    max-width: 98vw;
    padding: 2rem 1rem;
  }
}

@media (max-width: 800px) {
  #root {
    max-width: 100vw;
    padding: 1rem 0.5rem;
  }
}
