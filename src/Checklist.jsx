import React, { useState } from 'react';
import {
  Card, CardContent, <PERSON>po<PERSON>, Button, Box, LinearProgress,
  Dialog, DialogTitle, DialogContent, DialogActions, TextField,
  Tooltip, IconButton, Chip, FormControl, InputLabel, Select, MenuItem,
  Alert, Divider
} from '@mui/material';
import {
  AssignmentTurnedIn as AssignmentTurnedInIcon,
  Info as InfoIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Help as HelpIcon,
  Save as SaveIcon,
  Download as DownloadIcon
} from '@mui/icons-material';
import checklistData from './checklistData';
import { useApp } from './contexts/AppContext';
import { exportService } from './services/exportService';

// Stavy kontrolních bodů
const ITEM_STATES = {
  PENDING: 'pending',      // Čeká na kontrolu
  COMPLETED: 'completed',  // Úspěšně dokončeno
  FAILED: 'failed',        // Selhalo/problém
  SKIPPED: 'skipped'       // Přeskočeno/nedostupné
};

const getStateInfo = (state) => {
  switch (state) {
    case ITEM_STATES.COMPLETED:
      return { label: 'Dokončeno', color: 'success', icon: <CheckCircleIcon /> };
    case ITEM_STATES.FAILED:
      return { label: 'Problém', color: 'error', icon: <CancelIcon /> };
    case ITEM_STATES.SKIPPED:
      return { label: 'Přeskočeno', color: 'warning', icon: <HelpIcon /> };
    default:
      return { label: 'Čeká', color: 'default', icon: null };
  }
};

function Checklist() {
  const { state, actions } = useApp();
  const [itemStates, setItemStates] = useState({}); // {itemId: state}
  const [itemNotes, setItemNotes] = useState({}); // {itemId: note}
  const [dialogOpen, setDialogOpen] = useState(false);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [generalNote, setGeneralNote] = useState('');
  const [detailOpen, setDetailOpen] = useState(false);
  const [detailText, setDetailText] = useState('');
  const [exported, setExported] = useState(false);

  const handleStateChange = (itemId, newState) => {
    setItemStates(prev => ({
      ...prev,
      [itemId]: newState
    }));
  };

  const handleNoteChange = (itemId, note) => {
    setItemNotes(prev => ({
      ...prev,
      [itemId]: note
    }));
  };

  const handleSave = () => {
    setSaveDialogOpen(true);
  };

  const handleFinish = () => {
    setDialogOpen(true);
  };

  const saveChecklist = (isComplete = false) => {
    const checklistRecord = {
      id: Date.now(),
      completedAt: new Date().toISOString(),
      user: state.user,
      outage: state.selectedOutage,
      isComplete,
      checklist: checklistData.map(item => ({
        id: item.id,
        title: item.title,
        detail: item.detail,
        state: itemStates[item.id] || ITEM_STATES.PENDING,
        note: itemNotes[item.id] || ''
      })),
      generalNote: generalNote
    };

    actions.addChecklistRecord(checklistRecord);

    if (isComplete) {
      actions.showSnackbar('Checklist byl úspěšně dokončen!', 'success');
      setDialogOpen(false);
    } else {
      actions.showSnackbar('Checklist byl uložen', 'info');
      setSaveDialogOpen(false);
    }

    setExported(false);
  };

  const handleDialogClose = () => {
    saveChecklist(true);
  };

  const handleSaveDialogClose = () => {
    saveChecklist(false);
  };

  const handleDetailOpen = (detail) => {
    setDetailText(detail);
    setDetailOpen(true);
  };
  const handleDetailClose = () => {
    setDetailOpen(false);
    setDetailText('');
  };

  const handleExport = async () => {
    const result = {
      completedAt: new Date().toISOString(),
      user: state.user,
      outage: state.selectedOutage,
      generalNote: generalNote,
      checklist: checklistData.map(item => ({
        id: item.id,
        title: item.title,
        detail: item.detail,
        state: itemStates[item.id] || ITEM_STATES.PENDING,
        note: itemNotes[item.id] || ''
      }))
    };

    try {
      // Export do PDF
      await exportService.exportChecklistToPDF(result, state.selectedOutage, state.user);
      setExported(true);
      actions.showSnackbar('Checklist byl exportován do PDF', 'success');
    } catch (error) {
      // Fallback na JSON export
      exportService.exportToJSON(result, `ote-checklist-${new Date().toISOString().slice(0, 10)}`);
      setExported(true);
      actions.showSnackbar('Checklist byl exportován do JSON', 'success');
    }
  };

  // Výpočet pokroku
  const completedItems = Object.values(itemStates).filter(state =>
    state === ITEM_STATES.COMPLETED || state === ITEM_STATES.FAILED || state === ITEM_STATES.SKIPPED
  ).length;
  const progress = (completedItems / checklistData.length) * 100;

  return (
    <Card sx={{ boxShadow: 6, mb: 3, borderRadius: 4, maxWidth: { xs: '100vw', md: 700 }, margin: '0 auto', width: '100%', p: 3, background: 'linear-gradient(135deg, #f7fafd 80%, #e3f6f9 100%)' }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <AssignmentTurnedInIcon color="primary" sx={{ fontSize: 36, mr: 2 }} />
          <Typography variant="h4" sx={{ color: 'primary.main', fontWeight: 800 }}>
            Checklist odstávky
          </Typography>
        </Box>
        <LinearProgress variant="determinate" value={progress} sx={{ height: 12, borderRadius: 6, mb: 4, background: '#e3f6f9' }} />
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          {checklistData.map((item) => {
            const currentState = itemStates[item.id] || ITEM_STATES.PENDING;
            const stateInfo = getStateInfo(currentState);
            const currentNote = itemNotes[item.id] || '';

            return (
              <Card
                key={item.id}
                sx={{
                  border: currentState === ITEM_STATES.COMPLETED ? '2px solid #64A70B' :
                          currentState === ITEM_STATES.FAILED ? '2px solid #DF4661' :
                          currentState === ITEM_STATES.SKIPPED ? '2px solid #FFBF3F' :
                          '2px solid #e0e0e0',
                  borderRadius: 3,
                  transition: 'all 0.3s ease',
                  '&:hover': { transform: 'translateY(-2px)', boxShadow: 4 }
                }}
              >
                <CardContent sx={{ p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 2 }}>
                    <Typography
                      variant="h6"
                      sx={{
                        flexGrow: 1,
                        fontWeight: 600,
                        fontSize: 18,
                        color: currentState === ITEM_STATES.COMPLETED ? 'success.main' :
                               currentState === ITEM_STATES.FAILED ? 'error.main' :
                               'text.primary'
                      }}
                    >
                      {item.title}
                    </Typography>

                    <Chip
                      icon={stateInfo.icon}
                      label={stateInfo.label}
                      color={stateInfo.color}
                      size="small"
                    />

                    {item.detail && (
                      <Tooltip title="Zobrazit detail" arrow>
                        <IconButton
                          onClick={() => handleDetailOpen(item.detail)}
                          color="info"
                          size="small"
                        >
                          <InfoIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, alignItems: 'center', mb: 2 }}>
                    <FormControl size="small" sx={{ minWidth: 120 }}>
                      <InputLabel>Stav</InputLabel>
                      <Select
                        value={currentState}
                        label="Stav"
                        onChange={(e) => handleStateChange(item.id, e.target.value)}
                      >
                        <MenuItem value={ITEM_STATES.PENDING}>Čeká</MenuItem>
                        <MenuItem value={ITEM_STATES.COMPLETED}>Dokončeno</MenuItem>
                        <MenuItem value={ITEM_STATES.FAILED}>Problém</MenuItem>
                        <MenuItem value={ITEM_STATES.SKIPPED}>Přeskočeno</MenuItem>
                      </Select>
                    </FormControl>

                    <TextField
                      size="small"
                      label="Poznámka"
                      value={currentNote}
                      onChange={(e) => handleNoteChange(item.id, e.target.value)}
                      placeholder="Volitelná poznámka..."
                      sx={{ flexGrow: 1 }}
                    />
                  </Box>
                </CardContent>
              </Card>
            );
          })}
        </Box>

        <Divider sx={{ my: 4 }} />

        {/* Obecná poznámka */}
        <TextField
          fullWidth
          label="Obecná poznámka k checklistu"
          value={generalNote}
          onChange={(e) => setGeneralNote(e.target.value)}
          multiline
          rows={3}
          placeholder="Zde můžete přidat obecnou poznámku k celému checklistu..."
          sx={{ mb: 3 }}
        />

        {/* Tlačítka */}
        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
          <Button
            variant="outlined"
            color="primary"
            size="large"
            startIcon={<SaveIcon />}
            onClick={handleSave}
            sx={{ fontWeight: 600, minWidth: 150 }}
          >
            Uložit
          </Button>

          <Button
            variant="contained"
            color="success"
            size="large"
            startIcon={<CheckCircleIcon />}
            onClick={handleFinish}
            sx={{ fontWeight: 600, minWidth: 150 }}
          >
            Dokončit
          </Button>

          <Button
            variant="outlined"
            color="secondary"
            size="large"
            startIcon={<DownloadIcon />}
            onClick={handleExport}
            disabled={exported}
            sx={{ fontWeight: 600, minWidth: 150 }}
          >
            {exported ? 'Exportováno' : 'Export'}
          </Button>
        </Box>
        {/* Dialog pro dokončení */}
        <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Dokončit checklist</DialogTitle>
          <DialogContent>
            <Alert severity="info" sx={{ mb: 2 }}>
              Checklist bude označen jako dokončený a uložen do historie.
            </Alert>
            <Typography variant="body2" sx={{ mb: 2 }}>
              Dokončené položky: {completedItems} z {checklistData.length}
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setDialogOpen(false)}>Zrušit</Button>
            <Button onClick={handleDialogClose} color="success" variant="contained">
              Dokončit checklist
            </Button>
          </DialogActions>
        </Dialog>

        {/* Dialog pro uložení */}
        <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)} maxWidth="sm" fullWidth>
          <DialogTitle>Uložit checklist</DialogTitle>
          <DialogContent>
            <Alert severity="info" sx={{ mb: 2 }}>
              Checklist bude uložen a můžete v něm později pokračovat.
            </Alert>
            <Typography variant="body2">
              Vyplněné položky: {completedItems} z {checklistData.length}
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setSaveDialogOpen(false)}>Zrušit</Button>
            <Button onClick={handleSaveDialogClose} color="primary" variant="contained">
              Uložit
            </Button>
          </DialogActions>
        </Dialog>
        <Dialog open={detailOpen} onClose={handleDetailClose} maxWidth="xs" fullWidth>
          <DialogTitle>Detail bodu</DialogTitle>
          <DialogContent>
            <Typography>{detailText}</Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleDetailClose} color="primary">Zavřít</Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
}

export default Checklist; 