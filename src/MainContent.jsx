import React from 'react';
import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import WelcomeCard from './WelcomeCard';
import Dashboard from './Dashboard';

function MainContent({ section, user, setUser, folder, setFolder, setSection }) {
  return (
    <Box role="main" sx={{ flexGrow: 1, p: { xs: 1, md: 4 }, maxWidth: 1600, mx: 'auto', width: '100%' }}>
      {section === 'intro' && <WelcomeCard user={user} setUser={setUser} folder={folder} setFolder={setFolder} onComplete={() => setSection('outages')} />}
      {section === 'outages' && <Dashboard />}
      {section === 'checklist' && <Dashboard />}
      {section === 'history' && <Dashboard />}
      {section === 'settings' && (
        <Typography variant="h4" sx={{ color: 'primary.main', fontWeight: 700 }}>Nastavení</Typography>
      )}
    </Box>
  );
}

export default MainContent; 