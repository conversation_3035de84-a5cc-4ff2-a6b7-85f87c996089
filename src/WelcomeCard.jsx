import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>, Button, Box, TextField, InputAdornment, Alert, Paper } from '@mui/material';
import FolderOpenIcon from '@mui/icons-material/FolderOpen';
import PersonIcon from '@mui/icons-material/Person';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import { useApp } from './contexts/AppContext';
import { selectFolder } from './utils/folderUtils';
import FolderSupportInfo from './components/FolderSupportInfo';

function WelcomeCard() {
  const { state, actions } = useApp();
  const [name, setName] = useState(state.user || '');
  const [feedback, setFeedback] = useState(null);
  const [folderError, setFolderError] = useState(null);

  useEffect(() => {
    if (state.folder && name.trim()) {
      const timeout = setTimeout(() => actions.setSection('outages'), 1500);
      return () => clearTimeout(timeout);
    }
  }, [state.folder, name, actions]);

  const handleSelectFolder = async () => {
    setFolderError(null);

    try {
      const folderName = await selectFolder();
      actions.setFolder(folderName);
      setFeedback({ type: 'success', msg: 'Složka úspěšně vybrána.' });
      actions.showSnackbar('Složka byla úspěšně vybrána', 'success');
    } catch (error) {
      if (error.message !== 'Výběr složky byl zrušen') {
        setFolderError(error.message);
      }
    }
  };

  const handleNameSubmit = (e) => {
    e.preventDefault();
    if (name.trim()) {
      actions.setUser(name.trim());
      setFeedback({ type: 'success', msg: 'Jméno úspěšně nastaveno.' });
      actions.showSnackbar('Jméno bylo úspěšně nastaveno', 'success');
    } else {
      setFeedback({ type: 'error', msg: 'Zadejte jméno kontrolujícího.' });
    }
  };

  return (
    <Box sx={{
      minHeight: '100vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      background: 'none',
    }}>
      <Paper elevation={8} sx={{
        p: { xs: 4, md: 8 },
        mb: 6,
        width: '100%',
        maxWidth: 540,
        mx: 'auto',
        borderRadius: 7,
        background: 'none',
        boxShadow: '0 8px 32px 0 rgba(0,76,151,0.13)',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
      }} role="region" aria-label="Úvodní obrazovka">
        <img src="/Logo_OTE.jpg" alt="OTE logo" style={{ width: 240, height: 'auto', marginBottom: 32 }} />
        <Typography variant="h2" sx={{ mb: 2, color: 'primary.main', fontWeight: 900, textAlign: 'center', letterSpacing: 1, fontSize: 38 }}>
          Vítejte v aplikaci OTE
        </Typography>
        <Typography variant="body1" sx={{ mb: 3, textAlign: 'center', maxWidth: 420, color: 'text.secondary', fontSize: 20 }}>
          Tato aplikace slouží k evidenci a správě checklistů při odstávkách systému OTE. Pro pokračování vyberte složku pro ukládání dat a zadejte své jméno.
        </Typography>
        <FolderSupportInfo />
        <Button
          variant="contained"
          color="secondary"
          startIcon={<FolderOpenIcon />}
          onClick={handleSelectFolder}
          sx={{ mb: 4, fontWeight: 800, minWidth: 320, fontSize: 22, py: 2, borderRadius: 3, boxShadow: 4 }}
          aria-label="Vybrat složku pro ukládání dat"
        >
          {state.folder ? 'Složka vybrána' : 'Vybrat složku pro ukládání dat'}
        </Button>
        {folderError && <Alert severity="error" sx={{ mb: 2, width: '100%' }}>{folderError}</Alert>}
        <Box component="form" onSubmit={handleNameSubmit} sx={{
          display: 'flex', flexDirection: 'column', gap: 2, alignItems: 'center', width: '100%', mt: 1, mb: 2
        }} aria-label="Formulář pro zadání jména">
          <TextField
            label="Jméno kontrolujícího"
            value={name}
            onChange={e => setName(e.target.value)}
            required
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <PersonIcon color="primary" />
                </InputAdornment>
              ),
            }}
            sx={{ minWidth: 340, fontSize: 20, borderRadius: 3 }}
            size="large"
            aria-label="Jméno kontrolujícího"
          />
          <Button
            type="submit"
            variant="contained"
            color="primary"
            endIcon={<CheckCircleIcon />}
            size="large"
            sx={{ fontWeight: 800, minWidth: 200, fontSize: 22, py: 2, borderRadius: 3 }}
            disabled={!state.folder || !name.trim()}
            aria-label="Potvrdit jméno"
          >
            Potvrdit
          </Button>
        </Box>
        {feedback && (
          <Alert severity={feedback.type} sx={{ mt: 2, width: '100%', textAlign: 'center', fontSize: 18 }}>{feedback.msg}</Alert>
        )}
        {state.folder && name.trim() && (
          <Alert severity="success" sx={{ mt: 2, width: '100%', textAlign: 'center', fontSize: 18 }}>
            Složka a jméno úspěšně nastaveny. Pokračujte do sekce Odstávky.
          </Alert>
        )}
      </Paper>
    </Box>
  );
}

export default WelcomeCard; 