import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  Grid,
  LinearProgress
} from '@mui/material'
import {
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Visibility as VisibilityIcon,
  Download as DownloadIcon,
  History as HistoryIcon
} from '@mui/icons-material'
import { useApp } from '../contexts/AppContext'
import { exportService } from '../services/exportService'

function HistoryPanel() {
  const { state, actions } = useApp()
  const [selectedRecord, setSelectedRecord] = useState(null)
  const [detailDialogOpen, setDetailDialogOpen] = useState(false)

  const handleViewDetails = (record) => {
    setSelectedRecord(record)
    setDetailDialogOpen(true)
  }

  const handleExportRecord = async (record) => {
    try {
      await exportService.exportChecklistToPDF(record, record.outage, record.user)
      actions.showSnackbar('Historie byla exportována do PDF', 'success')
    } catch (error) {
      exportService.exportToJSON(record, `checklist-${record.id}`)
      actions.showSnackbar('Historie byla exportována do JSON', 'success')
    }
  }

  const handleExportAllHistory = async () => {
    try {
      await exportService.exportToExcel({ checklistHistory: state.checklistHistory }, 'historie-checklistu')
      actions.showSnackbar('Celá historie byla exportována do Excel', 'success')
    } catch (error) {
      exportService.exportHistoryToCSV(state.checklistHistory, 'historie-checklistu')
      actions.showSnackbar('Celá historie byla exportována do CSV', 'success')
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('cs-CZ', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getCompletionRate = (checklist) => {
    if (!checklist || checklist.length === 0) return 0
    const completed = checklist.filter(item => item.checked).length
    return Math.round((completed / checklist.length) * 100)
  }

  if (state.checklistHistory.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 8 }}>
        <HistoryIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h5" color="text.secondary" sx={{ mb: 2 }}>
          Zatím žádná historie
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Dokončené checklisty se budou zobrazovat zde.
        </Typography>
      </Box>
    )
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
      {/* Statistiky */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary.main">
                {state.checklistHistory.length}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Dokončených checklistů
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {Math.round(
                  state.checklistHistory.reduce((acc, record) => 
                    acc + getCompletionRate(record.checklist), 0
                  ) / state.checklistHistory.length
                )}%
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Průměrná úspěšnost
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main">
                {new Set(state.checklistHistory.map(r => r.user)).size}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Různých uživatelů
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="secondary.main">
                {new Set(state.checklistHistory.map(r => r.outage?.date)).size}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Různých odstávek
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tlačítko pro export celé historie */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          variant="contained"
          startIcon={<DownloadIcon />}
          onClick={handleExportAllHistory}
          color="secondary"
        >
          Exportovat celou historii
        </Button>
      </Box>

      {/* Tabulka s historií */}
      <TableContainer component={Paper} sx={{ boxShadow: 3 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: 'primary.light' }}>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Datum dokončení
              </TableCell>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Odstávka
              </TableCell>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Uživatel
              </TableCell>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Úspěšnost
              </TableCell>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Akce
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {state.checklistHistory
              .sort((a, b) => new Date(b.completedAt) - new Date(a.completedAt))
              .map((record) => {
                const completionRate = getCompletionRate(record.checklist)
                return (
                  <TableRow key={record.id} hover>
                    <TableCell>
                      {formatDate(record.completedAt)}
                    </TableCell>
                    <TableCell>
                      {record.outage ? (
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 600 }}>
                            {record.outage.date}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {record.outage.from} - {record.outage.to}
                          </Typography>
                        </Box>
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          Neznámá odstávka
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {record.user || 'Neznámý uživatel'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={completionRate}
                          sx={{ width: 60, height: 8, borderRadius: 4 }}
                          color={completionRate === 100 ? 'success' : completionRate >= 80 ? 'warning' : 'error'}
                        />
                        <Typography variant="body2" sx={{ minWidth: 40 }}>
                          {completionRate}%
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          size="small"
                          startIcon={<VisibilityIcon />}
                          onClick={() => handleViewDetails(record)}
                        >
                          Detail
                        </Button>
                        <Button
                          size="small"
                          startIcon={<DownloadIcon />}
                          onClick={() => handleExportRecord(record)}
                          color="secondary"
                        >
                          Export
                        </Button>
                      </Box>
                    </TableCell>
                  </TableRow>
                )
              })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Dialog s detaily checklistu */}
      <Dialog
        open={detailDialogOpen}
        onClose={() => setDetailDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Detail checklistu
          {selectedRecord && (
            <Typography variant="subtitle2" color="text.secondary">
              Dokončeno: {formatDate(selectedRecord.completedAt)}
            </Typography>
          )}
        </DialogTitle>
        <DialogContent>
          {selectedRecord && (
            <Box>
              {/* Informace o odstávce */}
              {selectedRecord.outage && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="subtitle2">
                    Odstávka: {selectedRecord.outage.date} {selectedRecord.outage.from}-{selectedRecord.outage.to}
                  </Typography>
                  <Typography variant="body2">
                    {selectedRecord.outage.note}
                  </Typography>
                </Alert>
              )}

              {/* Seznam kontrolních bodů */}
              <Typography variant="h6" sx={{ mb: 2 }}>
                Kontrolní body ({getCompletionRate(selectedRecord.checklist)}% dokončeno)
              </Typography>
              
              <List>
                {selectedRecord.checklist?.map((item, index) => (
                  <React.Fragment key={item.id}>
                    <ListItem>
                      <ListItemIcon>
                        {item.checked ? (
                          <CheckCircleIcon color="success" />
                        ) : (
                          <CancelIcon color="error" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={item.title}
                        secondary={item.note}
                        sx={{
                          textDecoration: item.checked ? 'none' : 'none',
                          opacity: item.checked ? 1 : 0.7
                        }}
                      />
                    </ListItem>
                    {index < selectedRecord.checklist.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>

              {/* Poznámky */}
              {selectedRecord.note && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="h6" sx={{ mb: 1 }}>
                    Poznámky
                  </Typography>
                  <Paper sx={{ p: 2, bgcolor: 'grey.50' }}>
                    <Typography variant="body2">
                      {selectedRecord.note}
                    </Typography>
                  </Paper>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailDialogOpen(false)}>
            Zavřít
          </Button>
          {selectedRecord && (
            <Button
              onClick={() => handleExportRecord(selectedRecord)}
              startIcon={<DownloadIcon />}
              color="primary"
            >
              Exportovat
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default HistoryPanel
