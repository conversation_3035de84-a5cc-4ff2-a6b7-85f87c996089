import React from 'react'
import { Box, Typography, Container } from '@mui/material'
import { useApp } from '../contexts/AppContext'
import EnhancedWelcomeCard from './EnhancedWelcomeCard'
import EnhancedOutageList from './EnhancedOutageList'
import Checklist from '../Checklist'
import HistoryPanel from './HistoryPanel'
import SettingsPanel from './SettingsPanel'

function MainContent() {
  const { state } = useApp()

  const renderContent = () => {
    switch (state.currentSection) {
      case 'intro':
        return <EnhancedWelcomeCard />
      
      case 'outages':
        return <EnhancedOutageList />
      
      case 'checklist':
        return (
          <>
            <Typography variant="h4" sx={{ mb: 3, color: 'primary.main', fontWeight: 700 }}>
              Kontrolní checklist
            </Typography>
            {state.user && state.folder && state.selectedOutage ? (
              <Checklist />
            ) : (
              <Box sx={{ 
                p: 4, 
                textAlign: 'center', 
                bgcolor: 'warning.light', 
                borderRadius: 2,
                color: 'warning.contrastText'
              }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Checklist není připraven
                </Typography>
                <Typography variant="body1">
                  Pro spuštění checklistu je potřeba:
                </Typography>
                <ul style={{ textAlign: 'left', marginTop: 16 }}>
                  <li>Nastavit složku pro ukládání dat {state.folder ? '✓' : '✗'}</li>
                  <li>Zadat jméno kontrolujícího {state.user ? '✓' : '✗'}</li>
                  <li>Vybrat odstávku {state.selectedOutage ? '✓' : '✗'}</li>
                </ul>
              </Box>
            )}
          </>
        )
      
      case 'history':
        return (
          <>
            <Typography variant="h4" sx={{ mb: 3, color: 'primary.main', fontWeight: 700 }}>
              Historie kontrol
            </Typography>
            <HistoryPanel />
          </>
        )
      
      case 'settings':
        return (
          <>
            <Typography variant="h4" sx={{ mb: 3, color: 'primary.main', fontWeight: 700 }}>
              Nastavení aplikace
            </Typography>
            <SettingsPanel />
          </>
        )
      
      default:
        return (
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Typography variant="h5" color="text.secondary">
              Sekce nenalezena
            </Typography>
          </Box>
        )
    }
  }

  return (
    <Container 
      maxWidth="xl" 
      sx={{ 
        py: 4,
        px: { xs: 2, md: 4 },
        minHeight: 'calc(100vh - 64px)',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      {renderContent()}
    </Container>
  )
}

export default MainContent
