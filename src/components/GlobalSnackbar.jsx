import React from 'react'
import { Snackbar, Alert } from '@mui/material'
import { useApp } from '../contexts/AppContext'

function GlobalSnackbar() {
  const { state, actions } = useApp()
  const { snackbar } = state.ui

  const handleClose = (event, reason) => {
    if (reason === 'clickaway') {
      return
    }
    actions.hideSnackbar()
  }

  return (
    <Snackbar
      open={snackbar.open}
      autoHideDuration={4000}
      onClose={handleClose}
      anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
    >
      <Alert
        elevation={6}
        variant="filled"
        onClose={handleClose}
        severity={snackbar.severity}
        sx={{ width: '100%' }}
      >
        {snackbar.message}
      </Alert>
    </Snackbar>
  )
}

export default GlobalSnackbar
