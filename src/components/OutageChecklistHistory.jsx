import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Typography,
  Chip,
  Box,
  Divider,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import {
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Help as HelpIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { checklistService } from '../services/checklistService';

const ITEM_STATES = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  FAILED: 'failed',
  SKIPPED: 'skipped'
};

const getStateInfo = (state) => {
  switch (state) {
    case ITEM_STATES.COMPLETED:
      return { label: 'Dokončeno', color: 'success', icon: <CheckCircleIcon /> };
    case ITEM_STATES.FAILED:
      return { label: 'Problém', color: 'error', icon: <CancelIcon /> };
    case ITEM_STATES.SKIPPED:
      return { label: 'Přesko<PERSON>eno', color: 'warning', icon: <HelpIcon /> };
    default:
      return { label: 'Čeká', color: 'default', icon: <ScheduleIcon /> };
  }
};

function OutageChecklistHistory({ open, onClose, outage }) {
  const [expandedUser, setExpandedUser] = useState(null);

  if (!outage) return null;

  const outageId = `${outage.date}_${outage.from}-${outage.to}`;
  const checklists = checklistService.getOutageChecklists(outageId);
  const users = Object.keys(checklists);

  const handleUserExpand = (userId) => {
    setExpandedUser(expandedUser === userId ? null : userId);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('cs-CZ');
  };

  const getChecklistStats = (checklist) => {
    const items = checklist.checklist || [];
    const completed = items.filter(item => item.state === ITEM_STATES.COMPLETED).length;
    const failed = items.filter(item => item.state === ITEM_STATES.FAILED).length;
    const skipped = items.filter(item => item.state === ITEM_STATES.SKIPPED).length;
    const pending = items.filter(item => item.state === ITEM_STATES.PENDING).length;
    
    return { completed, failed, skipped, pending, total: items.length };
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Typography variant="h6">
            Historie checklistů - {outage.date} {outage.from}-{outage.to}
          </Typography>
          <IconButton onClick={onClose}>
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      
      <DialogContent>
        {users.length === 0 ? (
          <Typography variant="body1" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
            Pro tuto odstávku zatím neexistují žádné checklisty.
          </Typography>
        ) : (
          <Box>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Celkem {users.length} kontrolujících osob
            </Typography>
            
            {users.map((userId) => {
              const checklist = checklists[userId];
              const stats = getChecklistStats(checklist);
              
              return (
                <Accordion 
                  key={userId}
                  expanded={expandedUser === userId}
                  onChange={() => handleUserExpand(userId)}
                  sx={{ mb: 1 }}
                >
                  <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%', mr: 2 }}>
                      <Box>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {userId}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Poslední úprava: {formatDate(checklist.lastModified)}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                        <Chip
                          label={checklist.isComplete ? 'Dokončeno' : 'Rozpracováno'}
                          color={checklist.isComplete ? 'success' : 'warning'}
                          size="small"
                        />
                        <Typography variant="caption" color="text.secondary">
                          {stats.completed + stats.failed + stats.skipped}/{stats.total}
                        </Typography>
                      </Box>
                    </Box>
                  </AccordionSummary>
                  
                  <AccordionDetails>
                    <Box>
                      {/* Statistiky */}
                      <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                        <Chip icon={<CheckCircleIcon />} label={`Dokončeno: ${stats.completed}`} color="success" size="small" />
                        <Chip icon={<CancelIcon />} label={`Problém: ${stats.failed}`} color="error" size="small" />
                        <Chip icon={<HelpIcon />} label={`Přeskočeno: ${stats.skipped}`} color="warning" size="small" />
                        <Chip icon={<ScheduleIcon />} label={`Čeká: ${stats.pending}`} color="default" size="small" />
                      </Box>
                      
                      {/* Obecná poznámka */}
                      {checklist.generalNote && (
                        <Box sx={{ mb: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                            Obecná poznámka:
                          </Typography>
                          <Typography variant="body2">
                            {checklist.generalNote}
                          </Typography>
                        </Box>
                      )}
                      
                      {/* Seznam položek s poznámkami */}
                      <List dense>
                        {checklist.checklist.map((item, index) => {
                          const stateInfo = getStateInfo(item.state);
                          
                          return (
                            <React.Fragment key={item.id}>
                              <ListItem>
                                <ListItemText
                                  primary={item.title}
                                  secondary={item.note || 'Bez poznámky'}
                                />
                                <ListItemSecondaryAction>
                                  <Chip
                                    icon={stateInfo.icon}
                                    label={stateInfo.label}
                                    color={stateInfo.color}
                                    size="small"
                                  />
                                </ListItemSecondaryAction>
                              </ListItem>
                              {index < checklist.checklist.length - 1 && <Divider />}
                            </React.Fragment>
                          );
                        })}
                      </List>
                    </Box>
                  </AccordionDetails>
                </Accordion>
              );
            })}
          </Box>
        )}
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Zavřít</Button>
      </DialogActions>
    </Dialog>
  );
}

export default OutageChecklistHistory;
