import React from 'react';
import { Alert, Typography, Box } from '@mui/material';
import { Info as InfoIcon, CheckCircle as CheckCircleIcon, Warning as WarningIcon } from '@mui/icons-material';
import { getFolderSupportInfo } from '../utils/folderUtils';

function FolderSupportInfo() {
  const supportInfo = getFolderSupportInfo();

  if (!supportInfo.supported) {
    return (
      <Alert 
        severity="error" 
        icon={<WarningIcon />}
        sx={{ mb: 2 }}
      >
        <Typography variant="body2">
          <strong>Výběr složky není podporován</strong><br />
          <PERSON><PERSON><PERSON> prohlížeč nepodporuje výběr složky. Doporučujeme použít moderní prohlížeč jako Chrome, Edge nebo Firefox.
        </Typography>
      </Alert>
    );
  }

  return (
    <Alert 
      severity={supportInfo.modern ? "success" : "info"} 
      icon={supportInfo.modern ? <CheckCircleIcon /> : <InfoIcon />}
      sx={{ mb: 2 }}
    >
      <Typography variant="body2">
        <strong>{supportInfo.message}</strong><br />
        {supportInfo.modern 
          ? "Váš prohlížeč podporuje moderní File System Access API pro bezpečný výběr složky."
          : "Váš prohlížeč podporuje výběr složky pomocí starší technologie. Funkčnost je plně zachována."
        }
      </Typography>
    </Alert>
  );
}

export default FolderSupportInfo;
