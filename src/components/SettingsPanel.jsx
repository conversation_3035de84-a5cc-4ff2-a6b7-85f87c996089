import React, { useState } from 'react'
import {
  Box,
  Card,
  CardContent,
  Typography,
  Switch,
  FormControlLabel,
  Button,
  Divider,
  Grid,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip
} from '@mui/material'
import {
  Download as DownloadIcon,
  Upload as UploadIcon,
  Delete as DeleteIcon,
  Folder as FolderIcon,
  Person as PersonIcon,
  Palette as PaletteIcon,
  Storage as StorageIcon
} from '@mui/icons-material'
import { useApp } from '../contexts/AppContext'
import { storageService } from '../services/storageService'
import { exportService } from '../services/exportService'

function SettingsPanel() {
  const { state, actions } = useApp()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [exportFormat, setExportFormat] = useState('json')
  const [importDialogOpen, setImportDialogOpen] = useState(false)

  // Získání velikosti uložených dat
  const storageSize = storageService.getStorageSize()

  const handleSettingChange = (setting, value) => {
    actions.updateSettings({ [setting]: value })
  }

  const handleExportData = async () => {
    const data = storageService.exportAllData()
    if (!data) {
      actions.showSnackbar('Chyba při exportu dat', 'error')
      return
    }

    try {
      switch (exportFormat) {
        case 'json':
          exportService.exportToJSON(data, 'ote-checklist-backup')
          break
        case 'excel':
          await exportService.exportToExcel(data, 'ote-checklist-backup')
          break
        case 'csv':
          exportService.exportHistoryToCSV(data.checklistHistory, 'ote-checklist-historie')
          break
        default:
          exportService.exportToJSON(data, 'ote-checklist-backup')
      }
      actions.showSnackbar('Data byla úspěšně exportována', 'success')
    } catch (error) {
      actions.showSnackbar('Chyba při exportu dat', 'error')
    }
  }

  const handleImportData = (event) => {
    const file = event.target.files[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result)
        const success = storageService.importAllData(data)
        if (success) {
          actions.showSnackbar('Data byla úspěšně importována', 'success')
          // Reload stránky pro načtení nových dat
          setTimeout(() => window.location.reload(), 1000)
        } else {
          actions.showSnackbar('Chyba při importu dat', 'error')
        }
      } catch (error) {
        actions.showSnackbar('Neplatný formát souboru', 'error')
      }
    }
    reader.readAsText(file)
    setImportDialogOpen(false)
  }

  const handleDeleteAllData = () => {
    const success = storageService.clearAppState()
    if (success) {
      actions.showSnackbar('Všechna data byla smazána', 'success')
      setTimeout(() => window.location.reload(), 1000)
    } else {
      actions.showSnackbar('Chyba při mazání dat', 'error')
    }
    setDeleteDialogOpen(false)
  }



  return (
    <Box sx={{ maxWidth: 1000, mx: 'auto' }}>
      <Grid container spacing={3}>
        {/* Obecné nastavení */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <PaletteIcon sx={{ mr: 1 }} />
                Vzhled a chování
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={state.settings.theme === 'dark'}
                      onChange={(e) => handleSettingChange('theme', e.target.checked ? 'dark' : 'light')}
                    />
                  }
                  label="Tmavý režim"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={state.settings.autoSave}
                      onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
                    />
                  }
                  label="Automatické ukládání"
                />
                
                <FormControlLabel
                  control={
                    <Switch
                      checked={state.settings.notifications}
                      onChange={(e) => handleSettingChange('notifications', e.target.checked)}
                    />
                  }
                  label="Zobrazovat notifikace"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Uživatelské nastavení */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <PersonIcon sx={{ mr: 1 }} />
                Uživatelské údaje
              </Typography>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <TextField
                  label="Jméno kontrolujícího"
                  value={state.user}
                  onChange={(e) => actions.setUser(e.target.value)}
                  fullWidth
                />
                

              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Export a import dat */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
                <StorageIcon sx={{ mr: 1 }} />
                Správa dat
              </Typography>
              
              <Alert severity="info" sx={{ mb: 2 }}>
                Velikost uložených dat: {storageSize.kb} KB ({storageSize.bytes} bytů)
              </Alert>
              
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <FormControl sx={{ minWidth: 120 }}>
                    <InputLabel>Formát exportu</InputLabel>
                    <Select
                      value={exportFormat}
                      onChange={(e) => setExportFormat(e.target.value)}
                      label="Formát exportu"
                    >
                      <MenuItem value="json">JSON</MenuItem>
                      <MenuItem value="excel">Excel</MenuItem>
                      <MenuItem value="csv">CSV (pouze historie)</MenuItem>
                    </Select>
                  </FormControl>
                  
                  <Button
                    variant="contained"
                    startIcon={<DownloadIcon />}
                    onClick={handleExportData}
                    color="primary"
                  >
                    Exportovat data
                  </Button>
                </Box>
                
                <Box sx={{ display: 'flex', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<UploadIcon />}
                    onClick={() => setImportDialogOpen(true)}
                    color="secondary"
                  >
                    Importovat data
                  </Button>
                  
                  <Button
                    variant="outlined"
                    startIcon={<DeleteIcon />}
                    onClick={() => setDeleteDialogOpen(true)}
                    color="error"
                  >
                    Smazat všechna data
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Statistiky */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2 }}>
                Statistiky aplikace
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="primary.main">
                      {state.outages.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Odstávky
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="secondary.main">
                      {state.checklistHistory.length}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Dokončené checklisty
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="success.main">
                      {storageSize.kb}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      KB dat
                    </Typography>
                  </Box>
                </Grid>
                
                <Grid item xs={6} sm={3}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h4" color="info.main">
                      {state.user ? '1' : '0'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Aktivní uživatel
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Dialog pro smazání dat */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Smazat všechna data?</DialogTitle>
        <DialogContent>
          <Typography>
            Tato akce smaže všechna uložená data včetně odstávek, historie checklistů a nastavení. 
            Tuto akci nelze vrátit zpět.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Zrušit</Button>
          <Button onClick={handleDeleteAllData} color="error" variant="contained">
            Smazat vše
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog pro import dat */}
      <Dialog open={importDialogOpen} onClose={() => setImportDialogOpen(false)}>
        <DialogTitle>Importovat data</DialogTitle>
        <DialogContent>
          <Typography sx={{ mb: 2 }}>
            Vyberte JSON soubor s exportovanými daty pro import.
          </Typography>
          <input
            type="file"
            accept=".json"
            onChange={handleImportData}
            style={{ width: '100%' }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setImportDialogOpen(false)}>Zrušit</Button>
        </DialogActions>
      </Dialog>
    </Box>
  )
}

export default SettingsPanel
