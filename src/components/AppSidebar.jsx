import React from 'react'
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Box,
  Divider,
  Typography,
  Chip
} from '@mui/material'
import {
  Home as HomeIcon,
  Assignment as AssignmentIcon,
  History as HistoryIcon,
  Settings as SettingsIcon,
  CheckCircle as CheckCircleIcon
} from '@mui/icons-material'
import { useApp } from '../contexts/AppContext'

const navItems = [
  { text: 'Úvod', icon: <HomeIcon />, section: 'intro' },
  { text: 'Odstávky', icon: <AssignmentIcon />, section: 'outages' },
  { text: 'Checklist', icon: <CheckCircleIcon />, section: 'checklist' },
  { text: 'Historie', icon: <HistoryIcon />, section: 'history' },
  { text: 'Nastavení', icon: <SettingsIcon />, section: 'settings' },
]

function AppSidebar() {
  const { state, actions } = useApp()

  const handleNavigation = (section) => {
    actions.setSection(section)
    actions.toggleDrawer() // Zavřít drawer po kliknutí
  }

  const getStatusChip = (section) => {
    switch (section) {
      case 'intro':
        return state.user ? (
          <Chip size="small" label="✓" color="success" />
        ) : null
      case 'outages':
        return state.outages.length > 0 ? (
          <Chip size="small" label={state.outages.length} color="primary" />
        ) : null
      case 'checklist':
        return state.selectedOutage ? (
          <Chip size="small" label="Připraven" color="info" />
        ) : null
      case 'history':
        return state.checklistHistory.length > 0 ? (
          <Chip size="small" label={state.checklistHistory.length} color="secondary" />
        ) : null
      default:
        return null
    }
  }

  return (
    <Drawer
      anchor="left"
      open={state.ui.drawerOpen}
      onClose={actions.toggleDrawer}
      sx={{
        '& .MuiDrawer-paper': {
          width: 280,
          boxSizing: 'border-box',
          pt: '64px', // Výška AppBar
        }
      }}
    >
      <Box sx={{ width: 280 }} role="presentation">
        {/* Hlavička sidebaru */}
        <Box sx={{ p: 2, bgcolor: 'primary.main', color: 'primary.contrastText' }}>
          <Typography variant="h6" sx={{ fontWeight: 700 }}>
            Navigace
          </Typography>
          {state.user && (
            <Typography variant="body2" sx={{ opacity: 0.8, mt: 0.5 }}>
              Přihlášen: {state.user}
            </Typography>
          )}
        </Box>

        <Divider />

        {/* Navigační položky */}
        <List>
          {navItems.map((item) => (
            <ListItem key={item.section} disablePadding>
              <ListItemButton
                selected={state.currentSection === item.section}
                onClick={() => handleNavigation(item.section)}
                sx={{
                  '&.Mui-selected': {
                    backgroundColor: 'primary.light',
                    color: 'primary.contrastText',
                    '&:hover': {
                      backgroundColor: 'primary.main',
                    }
                  }
                }}
              >
                <ListItemIcon 
                  sx={{ 
                    color: state.currentSection === item.section ? 'primary.contrastText' : 'inherit' 
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                <ListItemText 
                  primary={item.text}
                  sx={{ 
                    color: state.currentSection === item.section ? 'primary.contrastText' : 'inherit' 
                  }}
                />
                {getStatusChip(item.section)}
              </ListItemButton>
            </ListItem>
          ))}
        </List>

        <Divider />

        {/* Informace o stavu aplikace */}
        <Box sx={{ p: 2 }}>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 1 }}>
            Stav aplikace:
          </Typography>
          
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2">Složka:</Typography>
              <Chip 
                size="small" 
                label={state.folder ? "Nastavena" : "Nenastavena"} 
                color={state.folder ? "success" : "default"}
                variant="outlined"
              />
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2">Odstávky:</Typography>
              <Chip 
                size="small" 
                label={state.outages.length} 
                color="primary"
                variant="outlined"
              />
            </Box>
            
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Typography variant="body2">Historie:</Typography>
              <Chip 
                size="small" 
                label={state.checklistHistory.length} 
                color="secondary"
                variant="outlined"
              />
            </Box>
          </Box>
        </Box>
      </Box>
    </Drawer>
  )
}

export default AppSidebar
