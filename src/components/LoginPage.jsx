import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Button,
  InputAdornment,
  Alert
} from '@mui/material';
import {
  Person as PersonIcon,
  Login as LoginIcon
} from '@mui/icons-material';
import { useApp } from '../contexts/AppContext';

function LoginPage() {
  const { actions } = useApp();
  const [name, setName] = useState('');
  const [error, setError] = useState('');

  const handleLogin = (e) => {
    e.preventDefault();
    
    if (!name.trim()) {
      setError('Zadejte prosím své jméno');
      return;
    }

    if (name.trim().length < 2) {
      setError('Jméno musí mít alespoň 2 znaky');
      return;
    }

    actions.setUser(name.trim());
    actions.setSection('outages');
    actions.showSnackbar(`Přihlášen jako ${name.trim()}`, 'success');
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: 'linear-gradient(135deg, #f7fafd 0%, #e3f6f9 100%)',
        p: 2
      }}
    >
      <Card
        sx={{
          maxWidth: 400,
          width: '100%',
          boxShadow: 8,
          borderRadius: 4
        }}
      >
        <CardContent sx={{ p: 4 }}>
          {/* Logo a nadpis */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <img 
              src="/Logo_OTE.jpg" 
              alt="OTE logo" 
              style={{ 
                width: 120, 
                height: 'auto', 
                marginBottom: 24 
              }} 
            />
            <Typography 
              variant="h4" 
              sx={{ 
                color: 'primary.main', 
                fontWeight: 700,
                mb: 1
              }}
            >
              Evidence checklistů
            </Typography>
            <Typography 
              variant="body1" 
              sx={{ 
                color: 'text.secondary',
                fontSize: 16
              }}
            >
              Systém pro správu odstávek OTE
            </Typography>
          </Box>

          {/* Login formulář */}
          <Box component="form" onSubmit={handleLogin}>
            <TextField
              fullWidth
              label="Jméno kontrolujícího"
              value={name}
              onChange={(e) => {
                setName(e.target.value);
                setError('');
              }}
              placeholder="Zadejte své jméno"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PersonIcon color="primary" />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 3 }}
              autoFocus
              required
            />

            {error && (
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            )}

            <Button
              type="submit"
              fullWidth
              variant="contained"
              size="large"
              endIcon={<LoginIcon />}
              sx={{
                py: 1.5,
                fontSize: 16,
                fontWeight: 600,
                borderRadius: 2
              }}
            >
              Přihlásit se
            </Button>
          </Box>

          {/* Informace */}
          <Box sx={{ mt: 4, textAlign: 'center' }}>
            <Typography 
              variant="caption" 
              sx={{ 
                color: 'text.secondary',
                fontSize: 12
              }}
            >
              Vaše jméno bude uvedeno ve všech exportovaných checklistech
            </Typography>
          </Box>
        </CardContent>
      </Card>
    </Box>
  );
}

export default LoginPage;
