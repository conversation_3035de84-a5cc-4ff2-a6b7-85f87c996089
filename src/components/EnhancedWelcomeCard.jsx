import React, { useState, useEffect } from 'react'
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  TextField,
  <PERSON><PERSON>,
  Step,
  StepLabel,
  StepContent,
  Alert,
  Chip,
  Grid,
  Paper,
  LinearProgress
} from '@mui/material'
import {
  Person as PersonIcon,
  CheckCircle as CheckCircleIcon,
  ArrowForward as ArrowForwardIcon,
  Settings as SettingsIcon
} from '@mui/icons-material'
import { useApp } from '../contexts/AppContext'

const steps = [
  {
    label: 'Zadejte své jméno',
    description: '<PERSON>aš<PERSON> jméno bude uvedeno v checklistech jako kontrolující osoba.',
    icon: <PersonIcon />
  },
  {
    label: 'Aplikace je připravena',
    description: '<PERSON><PERSON><PERSON><PERSON> zač<PERSON>t pracovat s odstávkami a checklisty.',
    icon: <CheckCircleIcon />
  }
]

function EnhancedWelcomeCard() {
  const { state, actions } = useApp()
  const [activeStep, setActiveStep] = useState(0)
  const [name, setName] = useState(state.user || '')

  // Automatické přepínání kroků
  useEffect(() => {
    if (!state.folder && !state.user) {
      setActiveStep(0)
    } else if (state.folder && !state.user) {
      setActiveStep(1)
    } else if (state.folder && state.user) {
      setActiveStep(2)
      // Automatické přesměrování po dokončení
      const timeout = setTimeout(() => {
        actions.setSection('outages')
      }, 2000)
      return () => clearTimeout(timeout)
    }
  }, [state.folder, state.user, actions])



  const handleNameSubmit = (e) => {
    e.preventDefault()
    if (name.trim()) {
      actions.setUser(name.trim())
      actions.showSnackbar('Jméno bylo úspěšně nastaveno', 'success')
    }
  }

  const getStepStatus = (stepIndex) => {
    if (stepIndex === 0) return state.user ? 'completed' : 'active'
    if (stepIndex === 1) return state.user ? 'completed' : 'disabled'
    return 'disabled'
  }

  const getProgressValue = () => {
    return state.user ? 100 : 0
  }

  return (
    <Box sx={{
      minHeight: '80vh',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      py: 4
    }}>
      <Card sx={{
        maxWidth: 800,
        width: '100%',
        mx: 2,
        boxShadow: 8,
        borderRadius: 4
      }}>
        <CardContent sx={{ p: 4 }}>
          {/* Hlavička */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <img 
              src="/Logo_OTE.jpg" 
              alt="OTE logo" 
              style={{ width: 200, height: 'auto', marginBottom: 24 }} 
            />
            <Typography variant="h3" sx={{ 
              mb: 2, 
              color: 'primary.main', 
              fontWeight: 900,
              fontSize: { xs: 28, md: 36 }
            }}>
              Vítejte v aplikaci OTE
            </Typography>
            <Typography variant="h6" sx={{ 
              mb: 3, 
              color: 'text.secondary',
              maxWidth: 600,
              mx: 'auto'
            }}>
              Moderní aplikace pro evidenci a správu checklistů při odstávkách systému OTE
            </Typography>
            
            {/* Progress bar */}
            <Box sx={{ mb: 3 }}>
              <LinearProgress 
                variant="determinate" 
                value={getProgressValue()} 
                sx={{ 
                  height: 8, 
                  borderRadius: 4,
                  bgcolor: 'grey.200'
                }}
              />
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Dokončeno: {getProgressValue()}%
              </Typography>
            </Box>
          </Box>

          {/* Stepper */}
          <Stepper activeStep={activeStep} orientation="vertical">
            {steps.map((step, index) => (
              <Step key={step.label}>
                <StepLabel
                  icon={step.icon}
                  sx={{
                    '& .MuiStepIcon-root': {
                      color: getStepStatus(index) === 'completed' ? 'success.main' : 
                             getStepStatus(index) === 'active' ? 'primary.main' : 'grey.400'
                    }
                  }}
                >
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    {step.label}
                  </Typography>
                </StepLabel>
                <StepContent>
                  <Typography variant="body1" sx={{ mb: 2, color: 'text.secondary' }}>
                    {step.description}
                  </Typography>
                  
                  {/* Krok 1: Zadání jména */}
                  {index === 0 && (
                    <Box component="form" onSubmit={handleNameSubmit}>
                      <TextField
                        fullWidth
                        label="Jméno kontrolujícího"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Zadejte své jméno"
                        sx={{ mb: 2 }}
                        disabled={!!state.user}
                      />
                      
                      {!state.user && (
                        <Button
                          type="submit"
                          variant="contained"
                          size="large"
                          endIcon={<ArrowForwardIcon />}
                          disabled={!name.trim()}
                        >
                          Potvrdit jméno
                        </Button>
                      )}
                      
                      {state.user && (
                        <Alert severity="success">
                          <Typography variant="body2">
                            Přihlášen jako: <strong>{state.user}</strong>
                          </Typography>
                        </Alert>
                      )}
                    </Box>
                  )}

                  {/* Krok 2: Dokončení */}
                  {index === 1 && (
                    <Box>
                      <Alert severity="success" sx={{ mb: 3 }}>
                        <Typography variant="body1" sx={{ fontWeight: 600 }}>
                          Aplikace je připravena k použití!
                        </Typography>
                        <Typography variant="body2">
                          Budete automaticky přesměrováni na správu odstávek.
                        </Typography>
                      </Alert>
                      
                      <Grid container spacing={2}>
                        <Grid item xs={12} sm={6}>
                          <Button
                            fullWidth
                            variant="contained"
                            size="large"
                            onClick={() => actions.setSection('outages')}
                            color="primary"
                          >
                            Přejít na odstávky
                          </Button>
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <Button
                            fullWidth
                            variant="outlined"
                            size="large"
                            startIcon={<SettingsIcon />}
                            onClick={() => actions.setSection('settings')}
                          >
                            Nastavení
                          </Button>
                        </Grid>
                      </Grid>
                    </Box>
                  )}
                </StepContent>
              </Step>
            ))}
          </Stepper>

          {/* Informace o funkcích */}
          {getProgressValue() === 100 && (
            <Paper sx={{ p: 3, mt: 4, bgcolor: 'grey.50' }}>
              <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
                Co můžete dělat:
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Chip label="Odstávky" color="primary" size="small" sx={{ mr: 1 }} />
                    <Typography variant="body2">Správa plánovaných odstávek</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Chip label="Checklist" color="secondary" size="small" sx={{ mr: 1 }} />
                    <Typography variant="body2">Interaktivní kontrolní seznamy</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Chip label="Historie" color="success" size="small" sx={{ mr: 1 }} />
                    <Typography variant="body2">Přehled dokončených kontrol</Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <Chip label="Export" color="info" size="small" sx={{ mr: 1 }} />
                    <Typography variant="body2">PDF, Excel a JSON export</Typography>
                  </Box>
                </Grid>
              </Grid>
            </Paper>
          )}
        </CardContent>
      </Card>
    </Box>
  )
}

export default EnhancedWelcomeCard
