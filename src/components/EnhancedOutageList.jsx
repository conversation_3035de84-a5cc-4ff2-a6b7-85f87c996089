import React, { useState } from 'react'
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Fab,
  Badge
} from '@mui/material'
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  EventAvailable as EventAvailableIcon,
  Schedule as ScheduleIcon,
  CalendarToday as CalendarTodayIcon,
  FilterList as FilterListIcon
} from '@mui/icons-material'
import { useApp } from '../contexts/AppContext'

const templates = [
  { label: 'Pravidelná úterní odstávka', from: '17:00', to: '22:00', note: 'úterý' },
  { label: 'Mimořádná odstávka', from: '16:00', to: '21:00', note: 'mimořádn<PERSON> odstávka' },
  { label: '<PERSON><PERSON><PERSON><PERSON> údržba', from: '08:00', to: '18:00', note: 'v<PERSON><PERSON><PERSON> údržba' },
  { label: 'No<PERSON>n<PERSON> údržba', from: '22:00', to: '06:00', note: 'noční údržba' }
]

function AddOutageDialog({ open, onClose, onAdd, editingOutage = null }) {
  const [outage, setOutage] = useState(
    editingOutage || { date: '', from: '', to: '', note: '' }
  )
  const [template, setTemplate] = useState('')
  const [errors, setErrors] = useState({})

  const handleTemplateChange = (templateLabel) => {
    const selectedTemplate = templates.find(t => t.label === templateLabel)
    if (selectedTemplate) {
      setOutage(prev => ({
        ...prev,
        from: selectedTemplate.from,
        to: selectedTemplate.to,
        note: selectedTemplate.note
      }))
    }
    setTemplate(templateLabel)
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!/^\d{2}\.\d{2}\.\d{4}$/.test(outage.date)) {
      newErrors.date = 'Zadejte datum ve formátu DD.MM.RRRR'
    }
    
    if (!/^\d{2}:\d{2}$/.test(outage.from)) {
      newErrors.from = 'Zadejte čas ve formátu HH:MM'
    }
    
    if (!/^\d{2}:\d{2}$/.test(outage.to)) {
      newErrors.to = 'Zadejte čas ve formátu HH:MM'
    }
    
    if (!outage.note.trim()) {
      newErrors.note = 'Poznámka je povinná'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = () => {
    if (validateForm()) {
      onAdd(outage)
      setOutage({ date: '', from: '', to: '', note: '' })
      setTemplate('')
      setErrors({})
      onClose()
    }
  }

  const handleClose = () => {
    setOutage(editingOutage || { date: '', from: '', to: '', note: '' })
    setTemplate('')
    setErrors({})
    onClose()
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        {editingOutage ? 'Upravit odstávku' : 'Přidat novou odstávku'}
      </DialogTitle>
      <DialogContent>
        <Grid container spacing={3} sx={{ mt: 1 }}>
          {/* Šablony */}
          <Grid item xs={12}>
            <TextField
              select
              fullWidth
              label="Šablona (volitelné)"
              value={template}
              onChange={(e) => handleTemplateChange(e.target.value)}
            >
              <MenuItem value="">Bez šablony</MenuItem>
              {templates.map((t) => (
                <MenuItem key={t.label} value={t.label}>
                  {t.label}
                </MenuItem>
              ))}
            </TextField>
          </Grid>

          {/* Datum */}
          <Grid item xs={12} sm={6}>
            <TextField
              fullWidth
              label="Datum"
              value={outage.date}
              onChange={(e) => setOutage(prev => ({ ...prev, date: e.target.value }))}
              placeholder="DD.MM.RRRR"
              error={!!errors.date}
              helperText={errors.date}
            />
          </Grid>

          {/* Čas od */}
          <Grid item xs={6} sm={3}>
            <TextField
              fullWidth
              label="Čas od"
              value={outage.from}
              onChange={(e) => setOutage(prev => ({ ...prev, from: e.target.value }))}
              placeholder="HH:MM"
              error={!!errors.from}
              helperText={errors.from}
            />
          </Grid>

          {/* Čas do */}
          <Grid item xs={6} sm={3}>
            <TextField
              fullWidth
              label="Čas do"
              value={outage.to}
              onChange={(e) => setOutage(prev => ({ ...prev, to: e.target.value }))}
              placeholder="HH:MM"
              error={!!errors.to}
              helperText={errors.to}
            />
          </Grid>

          {/* Poznámka */}
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Poznámka"
              value={outage.note}
              onChange={(e) => setOutage(prev => ({ ...prev, note: e.target.value }))}
              multiline
              rows={2}
              error={!!errors.note}
              helperText={errors.note}
            />
          </Grid>
        </Grid>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose}>Zrušit</Button>
        <Button onClick={handleSubmit} variant="contained">
          {editingOutage ? 'Uložit změny' : 'Přidat odstávku'}
        </Button>
      </DialogActions>
    </Dialog>
  )
}

function EnhancedOutageList() {
  const { state, actions } = useApp()
  const [dialogOpen, setDialogOpen] = useState(false)
  const [editingOutage, setEditingOutage] = useState(null)
  const [filter, setFilter] = useState('')

  const handleAdd = (newOutage) => {
    if (editingOutage) {
      actions.updateOutage({ ...newOutage, id: editingOutage.id })
      actions.showSnackbar('Odstávka byla úspěšně upravena', 'success')
      setEditingOutage(null)
    } else {
      const id = state.outages.length ? Math.max(...state.outages.map(o => o.id)) + 1 : 1
      const outageWithId = { ...newOutage, id }
      actions.addOutage(outageWithId)
      actions.selectOutage(outageWithId)
      actions.showSnackbar('Odstávka byla úspěšně přidána', 'success')
    }
  }

  const handleEdit = (outage) => {
    setEditingOutage(outage)
    setDialogOpen(true)
  }

  const handleDelete = (id) => {
    if (window.confirm('Opravdu chcete smazat tuto odstávku?')) {
      actions.deleteOutage(id)
      actions.showSnackbar('Odstávka byla smazána', 'info')
    }
  }

  const handleSelect = (outage) => {
    actions.selectOutage(outage)
    actions.showSnackbar(`Vybrána odstávka: ${outage.date} ${outage.from}-${outage.to}`, 'info')
  }

  const filteredOutages = state.outages.filter(outage =>
    outage.date.toLowerCase().includes(filter.toLowerCase()) ||
    outage.note.toLowerCase().includes(filter.toLowerCase())
  )

  const getOutageStatus = (outage) => {
    const today = new Date()
    const [day, month, year] = outage.date.split('.')
    const outageDate = new Date(year, month - 1, day)
    
    if (outageDate < today) return 'past'
    if (outageDate.toDateString() === today.toDateString()) return 'today'
    return 'future'
  }

  const getStatusChip = (status) => {
    switch (status) {
      case 'past':
        return <Chip label="Proběhla" size="small" color="default" />
      case 'today':
        return <Chip label="Dnes" size="small" color="warning" />
      case 'future':
        return <Chip label="Plánovaná" size="small" color="primary" />
      default:
        return null
    }
  }

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto' }}>
      {/* Hlavička s filtrem */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ color: 'primary.main', fontWeight: 700 }}>
          Správa odstávek
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, alignItems: 'center' }}>
          <TextField
            size="small"
            placeholder="Filtrovat odstávky..."
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            InputProps={{
              startAdornment: <FilterListIcon sx={{ mr: 1, color: 'text.secondary' }} />
            }}
          />
          <Badge badgeContent={state.outages.length} color="primary">
            <CalendarTodayIcon />
          </Badge>
        </Box>
      </Box>

      {/* Vybraná odstávka */}
      {state.selectedOutage && (
        <Alert severity="info" sx={{ mb: 3 }}>
          <Typography variant="subtitle1">
            <strong>Vybraná odstávka:</strong> {state.selectedOutage.date} {state.selectedOutage.from}-{state.selectedOutage.to}
          </Typography>
          <Typography variant="body2">
            {state.selectedOutage.note}
          </Typography>
        </Alert>
      )}

      {/* Tabulka odstávek */}
      <TableContainer component={Paper} sx={{ boxShadow: 3, borderRadius: 2 }}>
        <Table>
          <TableHead>
            <TableRow sx={{ bgcolor: 'primary.light' }}>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Datum
              </TableCell>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Čas
              </TableCell>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Poznámka
              </TableCell>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Stav
              </TableCell>
              <TableCell sx={{ fontWeight: 700, color: 'primary.contrastText' }}>
                Akce
              </TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredOutages.map((outage) => {
              const status = getOutageStatus(outage)
              const isSelected = state.selectedOutage?.id === outage.id
              
              return (
                <TableRow
                  key={outage.id}
                  hover
                  selected={isSelected}
                  sx={{
                    bgcolor: isSelected ? 'primary.light' : 'inherit',
                    cursor: 'pointer',
                    '&:hover': { bgcolor: isSelected ? 'primary.light' : 'grey.50' }
                  }}
                  onClick={() => handleSelect(outage)}
                >
                  <TableCell>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ScheduleIcon sx={{ color: 'text.secondary' }} />
                      <Typography variant="body1" sx={{ fontWeight: isSelected ? 600 : 400 }}>
                        {outage.date}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2">
                      {outage.from} - {outage.to}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body2" sx={{ maxWidth: 200 }}>
                      {outage.note}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {getStatusChip(status)}
                  </TableCell>
                  <TableCell>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Tooltip title="Vybrat odstávku">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleSelect(outage)
                          }}
                          color={isSelected ? 'primary' : 'default'}
                        >
                          <EventAvailableIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Upravit">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEdit(outage)
                          }}
                        >
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Smazat">
                        <IconButton
                          size="small"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDelete(outage.id)
                          }}
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Floating Action Button */}
      <Fab
        color="primary"
        aria-label="add"
        sx={{ position: 'fixed', bottom: 24, right: 24 }}
        onClick={() => setDialogOpen(true)}
      >
        <AddIcon />
      </Fab>

      {/* Dialog pro přidání/úpravu */}
      <AddOutageDialog
        open={dialogOpen}
        onClose={() => {
          setDialogOpen(false)
          setEditingOutage(null)
        }}
        onAdd={handleAdd}
        editingOutage={editingOutage}
      />
    </Box>
  )
}

export default EnhancedOutageList
