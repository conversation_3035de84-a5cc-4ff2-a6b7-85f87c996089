import React from 'react'
import {
  <PERSON><PERSON>Bar,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Tooltip,
  Avatar,
  Button,
  Chip
} from '@mui/material'
import {
  Menu as MenuIcon,
  Brightness4 as Brightness4Icon,
  Brightness7 as Brightness7Icon,
  Settings as SettingsIcon,
  <PERSON><PERSON>ut as LogoutIcon,
  Person as PersonIcon
} from '@mui/icons-material'
import { useApp } from '../contexts/AppContext'

// Logo OTE komponenta
const OTELogo = () => (
  <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
    <svg 
      width="50" 
      height="28" 
      viewBox="0 0 180 96" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
    >
      <text 
        x="0" 
        y="72" 
        fontFamily="Carlito, Calibri, Arial, sans-serif" 
        fontWeight="bold" 
        fontSize="72" 
        fill="currentColor"
      >
        OTE
      </text>
      <polyline 
        points="120,72 130,50 145,90 160,30 175,72" 
        fill="none" 
        stroke="#00A9CE" 
        strokeWidth="7" 
        strokeLinejoin="round" 
      />
    </svg>
  </Box>
)

function AppHeader() {
  const { state, actions } = useApp()

  const handleThemeToggle = () => {
    const newTheme = state.settings.theme === 'light' ? 'dark' : 'light'
    actions.updateSettings({ theme: newTheme })
  }

  const handleMenuClick = () => {
    actions.toggleDrawer()
  }

  const handleSettingsClick = () => {
    actions.setSection('settings')
  }

  return (
    <AppBar 
      position="fixed" 
      color="primary" 
      elevation={2}
      sx={{ zIndex: (theme) => theme.zIndex.drawer + 1 }}
    >
      <Toolbar>
        {/* Menu tlačítko */}
        <Tooltip title="Otevřít menu">
          <IconButton
            edge="start"
            color="inherit"
            aria-label="menu"
            onClick={handleMenuClick}
            sx={{ mr: 2 }}
          >
            <MenuIcon />
          </IconButton>
        </Tooltip>

        {/* Logo */}
        <OTELogo />

        {/* Název aplikace */}
        <Typography 
          variant="h6" 
          component="div" 
          sx={{ 
            flexGrow: 1, 
            fontWeight: 700,
            display: { xs: 'none', sm: 'block' }
          }}
        >
          Evidence checklistů odstávek OTE
        </Typography>

        {/* Zkrácený název pro mobily */}
        <Typography 
          variant="h6" 
          component="div" 
          sx={{ 
            flexGrow: 1, 
            fontWeight: 700,
            display: { xs: 'block', sm: 'none' }
          }}
        >
          OTE Checklist
        </Typography>

        {/* Uživatelské informace */}
        {state.user && (
          <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
            <Avatar sx={{ width: 32, height: 32, mr: 1, bgcolor: 'secondary.main' }}>
              <PersonIcon />
            </Avatar>
            <Typography 
              variant="body2" 
              sx={{ 
                display: { xs: 'none', md: 'block' },
                color: 'inherit'
              }}
            >
              {state.user}
            </Typography>
          </Box>
        )}

        {/* Tlačítka v hlavičce */}
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          {/* Přepínač tématu */}
          <Tooltip title={`Přepnout na ${state.settings.theme === 'light' ? 'tmavý' : 'světlý'} režim`}>
            <IconButton
              color="inherit"
              onClick={handleThemeToggle}
              aria-label="toggle theme"
            >
              {state.settings.theme === 'dark' ? <Brightness7Icon /> : <Brightness4Icon />}
            </IconButton>
          </Tooltip>

          {/* Přihlášený uživatel */}
          {state.user && (
            <Chip
              avatar={<Avatar sx={{ bgcolor: 'secondary.main' }}>{state.user.charAt(0).toUpperCase()}</Avatar>}
              label={state.user}
              variant="outlined"
              sx={{
                color: 'white',
                borderColor: 'white',
                mr: 2,
                '& .MuiChip-avatar': { color: 'white' }
              }}
            />
          )}

          {/* Logout */}
          {state.user && (
            <Tooltip title="Odhlásit se">
              <IconButton
                color="inherit"
                onClick={actions.logout}
                aria-label="logout"
                sx={{ mr: 1 }}
              >
                <LogoutIcon />
              </IconButton>
            </Tooltip>
          )}

          {/* Nastavení */}
          <Tooltip title="Nastavení">
            <IconButton
              color="inherit"
              onClick={handleSettingsClick}
              aria-label="settings"
            >
              <SettingsIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Toolbar>
    </AppBar>
  )
}

export default AppHeader
