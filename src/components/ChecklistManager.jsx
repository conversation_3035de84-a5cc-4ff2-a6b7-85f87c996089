import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Divider,
  Alert
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useApp } from '../contexts/AppContext';

function ChecklistManager() {
  const { state, actions } = useApp();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({ title: '', detail: '' });
  const [customItems, setCustomItems] = useState([]);

  const handleAddItem = () => {
    setEditingItem(null);
    setFormData({ title: '', detail: '' });
    setDialogOpen(true);
  };

  const handleEditItem = (item) => {
    setEditingItem(item);
    setFormData({ title: item.title, detail: item.detail });
    setDialogOpen(true);
  };

  const handleSaveItem = () => {
    if (!formData.title.trim()) {
      actions.showSnackbar('Název je povinný', 'error');
      return;
    }

    const newItem = {
      id: editingItem ? editingItem.id : Date.now(),
      title: formData.title.trim(),
      detail: formData.detail.trim()
    };

    if (editingItem) {
      // Editace existující položky
      setCustomItems(prev => prev.map(item => 
        item.id === editingItem.id ? newItem : item
      ));
      actions.showSnackbar('Položka byla upravena', 'success');
    } else {
      // Přidání nové položky
      setCustomItems(prev => [...prev, newItem]);
      actions.showSnackbar('Položka byla přidána', 'success');
    }

    setDialogOpen(false);
    setFormData({ title: '', detail: '' });
    setEditingItem(null);
  };

  const handleDeleteItem = (itemId) => {
    setCustomItems(prev => prev.filter(item => item.id !== itemId));
    actions.showSnackbar('Položka byla smazána', 'success');
  };

  const handleSaveTemplate = () => {
    // Zde by se uložila šablona do localStorage nebo kontextu
    actions.showSnackbar('Šablona byla uložena', 'success');
  };

  return (
    <Box>
      <Typography variant="h4" sx={{ mb: 3, color: 'primary.main', fontWeight: 700 }}>
        Správa checklistu
      </Typography>

      <Alert severity="info" sx={{ mb: 3 }}>
        Zde můžete přidat vlastní kontrolní body nebo upravit stávající. 
        Změny se projeví ve všech nových checklistech.
      </Alert>

      {/* Standardní položky */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, color: 'primary.main' }}>
            Standardní kontrolní body
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Tyto body jsou součástí základní šablony a nelze je upravovat.
          </Typography>
          <Typography variant="body2">
            Celkem: 15 standardních kontrolních bodů
          </Typography>
        </CardContent>
      </Card>

      {/* Vlastní položky */}
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6" sx={{ color: 'primary.main' }}>
              Vlastní kontrolní body
            </Typography>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={handleAddItem}
            >
              Přidat bod
            </Button>
          </Box>

          {customItems.length === 0 ? (
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
              Zatím nemáte žádné vlastní kontrolní body.
            </Typography>
          ) : (
            <List>
              {customItems.map((item, index) => (
                <React.Fragment key={item.id}>
                  <ListItem>
                    <ListItemText
                      primary={item.title}
                      secondary={item.detail}
                    />
                    <ListItemSecondaryAction>
                      <IconButton
                        edge="end"
                        onClick={() => handleEditItem(item)}
                        sx={{ mr: 1 }}
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        edge="end"
                        onClick={() => handleDeleteItem(item.id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </ListItemSecondaryAction>
                  </ListItem>
                  {index < customItems.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          )}

          {customItems.length > 0 && (
            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Button
                variant="outlined"
                startIcon={<SaveIcon />}
                onClick={handleSaveTemplate}
              >
                Uložit jako šablonu
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Dialog pro přidání/editaci */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingItem ? 'Upravit kontrolní bod' : 'Přidat kontrolní bod'}
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Název kontrolního bodu"
            fullWidth
            variant="outlined"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            label="Detail/popis (volitelné)"
            fullWidth
            variant="outlined"
            multiline
            rows={3}
            value={formData.detail}
            onChange={(e) => setFormData(prev => ({ ...prev, detail: e.target.value }))}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)} startIcon={<CancelIcon />}>
            Zrušit
          </Button>
          <Button onClick={handleSaveItem} variant="contained" startIcon={<SaveIcon />}>
            {editingItem ? 'Uložit změny' : 'Přidat'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default ChecklistManager;
