/**
 * Univerz<PERSON>ln<PERSON> funkce pro výběr s<PERSON>, kter<PERSON> funguje ve všech prohlížeč<PERSON>ch
 * @returns {Promise<string>} Název vybrané složky
 */
export const selectFolder = () => {
  return new Promise((resolve, reject) => {
    // Pokusíme se použít moderní File System Access API
    if ('showDirectoryPicker' in window) {
      window.showDirectoryPicker()
        .then(dirHandle => {
          resolve(dirHandle.name);
        })
        .catch(error => {
          if (error.name === 'AbortError') {
            reject(new Error('Výběr složky byl zrušen'));
          } else {
            reject(new Error('Chyba při výběru složky: ' + error.message));
          }
        });
      return;
    }
    
    // Fallback pro starší prohlížeče - použijeme input element
    const input = document.createElement('input');
    input.type = 'file';
    input.webkitdirectory = true;
    input.directory = true;
    input.multiple = true;
    input.style.display = 'none';
    
    input.onchange = (e) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        // Získáme název složky z první cesty souboru
        const firstFile = files[0];
        const pathParts = firstFile.webkitRelativePath.split('/');
        const folderName = pathParts[0] || 'Vybraná složka';
        
        resolve(folderName);
      } else {
        reject(new Error('Nebyla vybrána žádná složka'));
      }
      
      // Vyčistíme DOM
      document.body.removeChild(input);
    };
    
    input.onerror = () => {
      reject(new Error('Chyba při výběru složky'));
      document.body.removeChild(input);
    };
    
    // Přidáme input do DOM a spustíme dialog
    document.body.appendChild(input);
    input.click();
  });
};

/**
 * Zkontroluje, zda je podporován výběr složky
 * @returns {boolean} True pokud je podporován výběr složky
 */
export const isFolderSelectionSupported = () => {
  return 'showDirectoryPicker' in window || 'webkitdirectory' in document.createElement('input');
};

/**
 * Získá informace o podpoře výběru složky
 * @returns {object} Objekt s informacemi o podpoře
 */
export const getFolderSupportInfo = () => {
  const hasModernAPI = 'showDirectoryPicker' in window;
  const hasLegacyAPI = 'webkitdirectory' in document.createElement('input');
  
  return {
    supported: hasModernAPI || hasLegacyAPI,
    modern: hasModernAPI,
    legacy: hasLegacyAPI,
    message: hasModernAPI 
      ? 'Podporován moderní File System Access API'
      : hasLegacyAPI 
        ? 'Podporován starší webkitdirectory API'
        : 'Výběr složky není podporován'
  };
};
