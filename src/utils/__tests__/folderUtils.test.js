/**
 * Testy pro folderUtils
 * Tyto testy ově<PERSON><PERSON><PERSON><PERSON> funkcionalitu výběru složky napříč r<PERSON>znými pro<PERSON>íž<PERSON>či
 */

import { isFolderSelectionSupported, getFolderSupportInfo } from '../folderUtils';

// Mock pro window objekt
const mockWindow = (hasShowDirectoryPicker = false) => {
  const originalWindow = global.window;
  
  if (hasShowDirectoryPicker) {
    global.window = {
      ...originalWindow,
      showDirectoryPicker: jest.fn()
    };
  } else {
    global.window = {
      ...originalWindow
    };
    delete global.window.showDirectoryPicker;
  }
  
  return () => {
    global.window = originalWindow;
  };
};

// Mock pro document.createElement
const mockCreateElement = (hasWebkitDirectory = false) => {
  const originalCreateElement = document.createElement;
  
  document.createElement = jest.fn((tagName) => {
    if (tagName === 'input') {
      const input = originalCreateElement.call(document, tagName);
      if (hasWebkitDirectory) {
        Object.defineProperty(input, 'webkitdirectory', {
          value: true,
          writable: true
        });
      }
      return input;
    }
    return originalCreateElement.call(document, tagName);
  });
  
  return () => {
    document.createElement = originalCreateElement;
  };
};

describe('folderUtils', () => {
  describe('isFolderSelectionSupported', () => {
    it('should return true when showDirectoryPicker is available', () => {
      const restore = mockWindow(true);
      const restoreCreateElement = mockCreateElement(false);
      
      expect(isFolderSelectionSupported()).toBe(true);
      
      restore();
      restoreCreateElement();
    });

    it('should return true when webkitdirectory is available', () => {
      const restore = mockWindow(false);
      const restoreCreateElement = mockCreateElement(true);
      
      expect(isFolderSelectionSupported()).toBe(true);
      
      restore();
      restoreCreateElement();
    });

    it('should return false when neither API is available', () => {
      const restore = mockWindow(false);
      const restoreCreateElement = mockCreateElement(false);
      
      expect(isFolderSelectionSupported()).toBe(false);
      
      restore();
      restoreCreateElement();
    });
  });

  describe('getFolderSupportInfo', () => {
    it('should return modern API info when showDirectoryPicker is available', () => {
      const restore = mockWindow(true);
      const restoreCreateElement = mockCreateElement(true);
      
      const info = getFolderSupportInfo();
      
      expect(info.supported).toBe(true);
      expect(info.modern).toBe(true);
      expect(info.legacy).toBe(true);
      expect(info.message).toBe('Podporován moderní File System Access API');
      
      restore();
      restoreCreateElement();
    });

    it('should return legacy API info when only webkitdirectory is available', () => {
      const restore = mockWindow(false);
      const restoreCreateElement = mockCreateElement(true);
      
      const info = getFolderSupportInfo();
      
      expect(info.supported).toBe(true);
      expect(info.modern).toBe(false);
      expect(info.legacy).toBe(true);
      expect(info.message).toBe('Podporován starší webkitdirectory API');
      
      restore();
      restoreCreateElement();
    });

    it('should return unsupported info when no API is available', () => {
      const restore = mockWindow(false);
      const restoreCreateElement = mockCreateElement(false);
      
      const info = getFolderSupportInfo();
      
      expect(info.supported).toBe(false);
      expect(info.modern).toBe(false);
      expect(info.legacy).toBe(false);
      expect(info.message).toBe('Výběr složky není podporován');
      
      restore();
      restoreCreateElement();
    });
  });
});
