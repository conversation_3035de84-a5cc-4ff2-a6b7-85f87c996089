import React, { createContext, useContext, useReducer, useEffect } from 'react'
import { storageService } from '../services/storageService'

// Výchozí odstávky
const defaultOutages = [
  { id: 1, date: '21.01.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 2, date: '18.02.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 3, date: '18.03.2025', from: '16:00', to: '21:00', note: 'mimo<PERSON><PERSON><PERSON> o<PERSON>táv<PERSON>, úterý' },
  { id: 4, date: '25.03.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 5, date: '22.04.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 6, date: '20.05.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 7, date: '24.06.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 8, date: '22.07.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 9, date: '26.08.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 10, date: '23.09.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 11, date: '21.10.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 12, date: '25.11.2025', from: '17:00', to: '22:00', note: 'úterý' },
  { id: 13, date: '16.12.2025', from: '17:00', to: '22:00', note: 'úterý' },
]

// Počáteční stav aplikace
const initialState = {
  user: '',
  currentSection: 'intro',
  outages: defaultOutages,
  selectedOutage: null,
  checklistHistory: [],
  settings: {
    theme: 'light',
    autoSave: true,
    notifications: true,
    exportFormat: 'json'
  },
  ui: {
    drawerOpen: false,
    snackbar: { open: false, message: '', severity: 'success' }
  }
}

// Akce pro reducer
const actionTypes = {
  SET_USER: 'SET_USER',
  SET_SECTION: 'SET_SECTION',
  SET_OUTAGES: 'SET_OUTAGES',
  ADD_OUTAGE: 'ADD_OUTAGE',
  UPDATE_OUTAGE: 'UPDATE_OUTAGE',
  DELETE_OUTAGE: 'DELETE_OUTAGE',
  SELECT_OUTAGE: 'SELECT_OUTAGE',
  ADD_CHECKLIST_RECORD: 'ADD_CHECKLIST_RECORD',
  UPDATE_SETTINGS: 'UPDATE_SETTINGS',
  TOGGLE_DRAWER: 'TOGGLE_DRAWER',
  SHOW_SNACKBAR: 'SHOW_SNACKBAR',
  HIDE_SNACKBAR: 'HIDE_SNACKBAR',
  LOAD_STATE: 'LOAD_STATE'
}

// Reducer pro správu stavu
function appReducer(state, action) {
  switch (action.type) {
    case actionTypes.SET_USER:
      return { ...state, user: action.payload }

    case actionTypes.SET_SECTION:
      return { ...state, currentSection: action.payload }
    
    case actionTypes.SET_OUTAGES:
      return { ...state, outages: action.payload }
    
    case actionTypes.ADD_OUTAGE:
      return { ...state, outages: [...state.outages, action.payload] }
    
    case actionTypes.UPDATE_OUTAGE:
      return {
        ...state,
        outages: state.outages.map(outage =>
          outage.id === action.payload.id ? action.payload : outage
        )
      }
    
    case actionTypes.DELETE_OUTAGE:
      return {
        ...state,
        outages: state.outages.filter(outage => outage.id !== action.payload),
        selectedOutage: state.selectedOutage?.id === action.payload ? null : state.selectedOutage
      }
    
    case actionTypes.SELECT_OUTAGE:
      return { ...state, selectedOutage: action.payload }
    
    case actionTypes.ADD_CHECKLIST_RECORD:
      return {
        ...state,
        checklistHistory: [...state.checklistHistory, action.payload]
      }
    
    case actionTypes.UPDATE_SETTINGS:
      return {
        ...state,
        settings: { ...state.settings, ...action.payload }
      }
    
    case actionTypes.TOGGLE_DRAWER:
      return {
        ...state,
        ui: { ...state.ui, drawerOpen: !state.ui.drawerOpen }
      }
    
    case actionTypes.SHOW_SNACKBAR:
      return {
        ...state,
        ui: {
          ...state.ui,
          snackbar: { open: true, ...action.payload }
        }
      }
    
    case actionTypes.HIDE_SNACKBAR:
      return {
        ...state,
        ui: { ...state.ui, snackbar: { ...state.ui.snackbar, open: false } }
      }
    
    case actionTypes.LOAD_STATE:
      return { ...state, ...action.payload }
    
    default:
      return state
  }
}

// Context
const AppContext = createContext()

// Provider komponenta
export function AppProvider({ children }) {
  const [state, dispatch] = useReducer(appReducer, initialState)

  // Načtení dat při startu aplikace
  useEffect(() => {
    const savedState = storageService.loadAppState()
    if (savedState) {
      dispatch({ type: actionTypes.LOAD_STATE, payload: savedState })
    }
  }, [])

  // Automatické ukládání při změnách stavu
  useEffect(() => {
    if (state.settings.autoSave) {
      storageService.saveAppState(state)
    }
  }, [state, state.settings.autoSave])

  // Akce pro komponenty
  const actions = {
    setUser: (user) => dispatch({ type: actionTypes.SET_USER, payload: user }),
    setSection: (section) => dispatch({ type: actionTypes.SET_SECTION, payload: section }),
    setOutages: (outages) => dispatch({ type: actionTypes.SET_OUTAGES, payload: outages }),
    addOutage: (outage) => dispatch({ type: actionTypes.ADD_OUTAGE, payload: outage }),
    updateOutage: (outage) => dispatch({ type: actionTypes.UPDATE_OUTAGE, payload: outage }),
    deleteOutage: (id) => dispatch({ type: actionTypes.DELETE_OUTAGE, payload: id }),
    selectOutage: (outage) => dispatch({ type: actionTypes.SELECT_OUTAGE, payload: outage }),
    addChecklistRecord: (record) => dispatch({ type: actionTypes.ADD_CHECKLIST_RECORD, payload: record }),
    updateSettings: (settings) => dispatch({ type: actionTypes.UPDATE_SETTINGS, payload: settings }),
    toggleDrawer: () => dispatch({ type: actionTypes.TOGGLE_DRAWER }),
    showSnackbar: (message, severity = 'success') => 
      dispatch({ type: actionTypes.SHOW_SNACKBAR, payload: { message, severity } }),
    hideSnackbar: () => dispatch({ type: actionTypes.HIDE_SNACKBAR })
  }

  return (
    <AppContext.Provider value={{ state, actions }}>
      {children}
    </AppContext.Provider>
  )
}

// Hook pro použití contextu
export function useApp() {
  const context = useContext(AppContext)
  if (!context) {
    throw new Error('useApp must be used within AppProvider')
  }
  return context
}

export { actionTypes }
