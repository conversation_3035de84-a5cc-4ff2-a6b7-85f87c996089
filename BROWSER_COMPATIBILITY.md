# Kompatibilita prohlížečů - Evidence Checklist

## Podporované prohlížeče

Aplikace Evidence Checklist je navr<PERSON><PERSON> tak, aby fungovala ve všech moderních prohlížečích. Výb<PERSON>r složky je implementován s fallback mechanismem pro maximální kompatibilitu.

### ✅ Plně podporované prohlížeče

**Moderní File System Access API:**
- Google Chrome 86+
- Microsoft Edge 86+
- Opera 72+

**Fallback webkitdirectory API:**
- Mozilla Firefox 50+
- Safari 11.1+
- Starší verze Chrome a Edge
- Většina mobilních prohlížečů

### 🔧 Technické detaily

#### Moderní API (File System Access)
- Poskytuje nejlepší uživatelský zážitek
- Bezpečný přístup k souborovému systému
- Nativní dialog pro výběr složky

#### Fallback API (webkitdirectory)
- Kompatibilní se staršími prohlížeči
- Používá standardní file input s atributem webkitdirectory
- <PERSON><PERSON><PERSON>, jen s mírně odlišným UI

### 🚫 Nepodporované prohlížeče

- Internet Explorer (všechny verze)
- Velmi staré verze mobilních prohlížečů (před 2018)

### 💡 Doporučení

Pro nejlepší zážitek doporučujeme používat:
1. **Google Chrome** (nejnovější verze)
2. **Microsoft Edge** (nejnovější verze)
3. **Mozilla Firefox** (nejnovější verze)

### 🔍 Jak zjistit, zda váš prohlížeč podporuje výběr složky

Aplikace automaticky detekuje podporu a zobrazí příslušnou informaci na úvodní stránce. Pokud vidíte chybovou zprávu o nepodpoře, aktualizujte prosím váš prohlížeč na nejnovější verzi.

### 📱 Mobilní zařízení

Výběr složky na mobilních zařízeních může být omezen operačním systémem. Doporučujeme používat aplikaci na desktop/laptop počítačích pro nejlepší funkcionalitu.
